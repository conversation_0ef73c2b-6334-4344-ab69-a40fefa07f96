<?php

namespace App\Http\Controllers;

use App\Models\ReceivablePayment;
use App\Models\Mitra;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class ReceivablePaymentController extends Controller
{
    public function index(Request $request)
    {
        $perPage = (int) $request->query('per_page', 20);
        $q = ReceivablePayment::with(['mitra:id,name,owner_id'])
            ->orderByDesc('paid_at')
            ->orderByDesc('created_at');

        $user = $request->user();
        if ($user && $user->role !== 'superadmin') {
            $ownerScopeId = $user->role === 'owner' ? $user->id : $user->owner_id;
            if ($ownerScopeId) {
                $q->whereHas('mitra', function ($w) use ($ownerScopeId) {
                    $w->where('owner_id', $ownerScopeId);
                });
            } else {
                $q->where('created_by', $user->id);
            }
        }

        if ($mitraId = $request->query('mitra_id')) {
            $q->where('mitra_id', (int) $mitraId);
        }

        return $q->paginate($perPage);
    }

    public function store(Request $request)
    {
        $data = $request->validate([
            'mitra_id' => ['required', 'integer', 'exists:mitras,id'],
            'transaction_id' => ['nullable', 'integer', 'exists:transactions,id'],
            'amount' => ['required', 'integer', 'min:1'],
            'paid_at' => ['nullable', 'date'],
            'note' => ['nullable', 'string', 'max:255']
        ]);

        $user = $request->user();
        return DB::transaction(function () use ($data, $user) {
            // Scope check: payment only for same-owner mitra for non-superadmin
            if ($user && $user->role !== 'superadmin') {
                $ownerScopeId = $user->role === 'owner' ? $user->id : $user->owner_id;
                $mitra = Mitra::findOrFail($data['mitra_id']);
                if (!$ownerScopeId || $mitra->owner_id !== $ownerScopeId) {
                    abort(403, 'Forbidden');
                }
            }

            $payment = ReceivablePayment::create([
                'mitra_id' => (int) $data['mitra_id'],
                'transaction_id' => $data['transaction_id'] ?? null,
                'amount' => (int) $data['amount'],
                'paid_at' => $data['paid_at'] ?? now(),
                'note' => $data['note'] ?? null,
                'created_by' => $user->id ?? null,
            ]);

            // Decrease current_debt safely
            $mitra = Mitra::lockForUpdate()->find($payment->mitra_id);
            if ($mitra) {
                $newDebt = max(0, (int) $mitra->current_debt - (int) $payment->amount);
                $mitra->update(['current_debt' => $newDebt]);
            }

            return response()->json($payment->load('mitra'), 201);
        });
    }
}

