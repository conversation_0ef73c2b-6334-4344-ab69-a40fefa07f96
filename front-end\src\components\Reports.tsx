import React, { useState } from 'react';
import { apiFetch } from '../services/api';
import { BarChart3, TrendingUp, Users, Package, Calendar, Download, PieChart } from 'lucide-react';
import Chart from 'react-apexcharts';
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';

type MonthlySalesEntry = { month: number; total: number };
type TopProduct = { product_id: number; qty: number; product?: { id: number; name: string } };
type TopMitra = { mitra_id: number; name: string; total: number; cnt?: number };

type Metrics = {
  monthly_sales?: MonthlySalesEntry[];
  top_products?: TopProduct[];
  top_mitras?: TopMitra[];
};

// Helper: export a DOM section to PDF with crisp header and proper margins
const fetchSettingsSafe = async () => {
  try {
    const s = await apiFetch('/api/settings');
    return {
      biz: s?.business_name || 'Agricultural Cashier System',
      addr: s?.address || '',
      logo: s?.logo_url || '/logo.png',
    };
  } catch {
    return { biz: 'Agricultural Cashier System', addr: '', logo: null };
  }
};

const toDataURL = async (url: string): Promise<string | null> => {
  try {
    const res = await fetch(url, { mode: 'cors' });
    const blob = await res.blob();
    return await new Promise((resolve) => {
      const reader = new FileReader();
      reader.onloadend = () => resolve(String(reader.result));
      reader.readAsDataURL(blob);
    });
  } catch {
    return null;
  }
};

const exportSectionToPdf = async (rootId: string, subtitle: string) => {
  const doc = new jsPDF('p', 'pt', 'a4');
  const root = document.getElementById(rootId);
  if (!root) return;

  // Header (kop surat)
  const { biz, addr, logo } = await fetchSettingsSafe();
  if (logo) {
    const dataUrl = await toDataURL(logo);
    if (dataUrl) {
      try { doc.addImage(dataUrl, 'PNG', 40, 24, 36, 36); } catch { }
    }
  }
  doc.setFont('helvetica', 'bold');
  doc.setFontSize(14);
  doc.text(biz, logo ? 88 : 40, 36);
  doc.setFont('helvetica', 'normal');
  doc.setFontSize(10);
  doc.text(`${subtitle}${addr ? ' — ' + addr : ''}`, logo ? 88 : 40, 52);
  // Separator line
  doc.setDrawColor(200);
  doc.line(40, 60, doc.internal.pageSize.getWidth() - 40, 60);

  // Render content section with better resolution
  const marginTop = 70;
  const marginX = 20;
  const pageWidth = doc.internal.pageSize.getWidth();
  const usableWidth = pageWidth - marginX * 2;
  const canvas = await html2canvas(root as HTMLElement, { scale: 2, useCORS: true, backgroundColor: '#ffffff' });
  const imgData = canvas.toDataURL('image/png');
  const imgWidth = usableWidth;
  const imgHeight = (canvas.height * imgWidth) / canvas.width;

  // Simple paging if content taller than one page
  const pageHeight = doc.internal.pageSize.getHeight();
  let heightLeft = imgHeight;
  let position = marginTop;

  doc.addImage(imgData, 'PNG', marginX, position, imgWidth, imgHeight);
  heightLeft -= (pageHeight - marginTop);

  while (heightLeft > 0) {
    position = 40; // top margin for next pages
    doc.addPage();
    // Add the same image but shift vertically by using a negative y to simulate crop
    doc.addImage(imgData, 'PNG', marginX, position - (imgHeight - heightLeft), imgWidth, imgHeight);
    heightLeft -= pageHeight - 40;
  }

  doc.save('laporan.pdf');
};
// Add multiple sections (best outlets + transactions history) into a single PDF
const addHeader = async (doc: jsPDF, subtitle: string) => {
  const { biz, addr, logo } = await fetchSettingsSafe();
  if (logo) {
    const dataUrl = await toDataURL(logo);
    if (dataUrl) {
      try { doc.addImage(dataUrl, 'PNG', 40, 24, 36, 36); } catch { }
    }
  }
  doc.setFont('helvetica', 'bold');
  doc.setFontSize(14);
  doc.text(biz, 88, 36);
  doc.setFont('helvetica', 'normal');
  doc.setFontSize(10);
  doc.text(`${subtitle}${addr ? ' — ' + addr : ''}`, 88, 52);
  doc.setDrawColor(200);
  doc.line(40, 60, doc.internal.pageSize.getWidth() - 40, 60);
};

const addSectionImagePages = async (doc: jsPDF, rootId: string) => {
  const root = document.getElementById(rootId);
  if (!root) return;
  const pageWidth = doc.internal.pageSize.getWidth();
  const pageHeight = doc.internal.pageSize.getHeight();
  const marginTop = 70;
  const marginX = 20;
  const usableWidth = pageWidth - marginX * 2;
  const canvas = await html2canvas(root as HTMLElement, { scale: 2, useCORS: true, backgroundColor: '#ffffff' });
  const imgData = canvas.toDataURL('image/png');
  const imgWidth = usableWidth;
  const imgHeight = (canvas.height * imgWidth) / canvas.width;
  let heightLeft = imgHeight;
  let position = marginTop;
  doc.addImage(imgData, 'PNG', marginX, position, imgWidth, imgHeight);
  heightLeft -= (pageHeight - marginTop);
  while (heightLeft > 0) {
    position = 40;
    doc.addPage();
    doc.addImage(imgData, 'PNG', marginX, position - (imgHeight - heightLeft), imgWidth, imgHeight);
    heightLeft -= pageHeight - 40;
  }
};

const exportOutletsAndHistoryToPdf = async () => {
  // Hide elements that should not appear in PDF
  const hidden = Array.from(document.querySelectorAll('.no-export')) as HTMLElement[];
  const prev = hidden.map(el => el.getAttribute('style') || '');
  hidden.forEach(el => { (el as HTMLElement).style.display = 'none'; });
  try {
    const doc = new jsPDF('p', 'pt', 'a4');
    await addHeader(doc, 'Outlet Terbaik');
    await addSectionImagePages(doc, 'reports-root');
    doc.addPage();
    await addHeader(doc, 'Riwayat Transaksi');
    await addSectionImagePages(doc, 'tx-history-root');
    doc.save('laporan.pdf');
  } finally {
    // Restore hidden elements
    hidden.forEach((el, i) => {
      const val = prev[i];
      if (val) el.setAttribute('style', val); else el.removeAttribute('style');
    });
  }
};


const Reports: React.FC = () => {
  const [dateFilter, setDateFilter] = useState('this-month');
  const [reportType, setReportType] = useState('sales');
  const [categoryId, setCategoryId] = React.useState<string>('');
  const [productId, setProductId] = React.useState<string>('');
  const [categories, setCategories] = React.useState<any[]>([]);
  const [products, setProducts] = React.useState<any[]>([]);
  const [monthlySeries, setMonthlySeries] = React.useState<any>({ months: [], series: [] });
  const [top10, setTop10] = React.useState<any[]>([]);
  const [topSales, setTopSales] = React.useState<any[]>([]);
  const [topOutlets, setTopOutlets] = React.useState<any[]>([]);
  const [outletProducts, setOutletProducts] = React.useState<any[]>([]);
  const [selectedOutlet, setSelectedOutlet] = React.useState<any | null>(null);

  const [transactions, setTransactions] = React.useState<any[]>([]);
  const [metrics, setMetrics] = React.useState<Metrics | null>(null);
  const [loading, setLoading] = React.useState(false);

  // Load filters and analytics when category/product changes
  React.useEffect(() => {
    (async () => {
      try {
        const cats = await apiFetch('/api/categories');
        const catItems: any[] = cats.data ?? cats;
        setCategories(catItems);
        if (categoryId) {
          const prods = await apiFetch('/api/products');
          const prodItems: any[] = prods.data ?? prods;
          setProducts(prodItems.filter((p: any) => String(p.category_id ?? p.categoryId) === String(categoryId)));

          const monthly = await apiFetch(`/api/analytics/products/monthly?category_id=${categoryId}${productId ? `&product_id=${productId}` : ''}`);
          setMonthlySeries(monthly);

          const topTen = await apiFetch(`/api/analytics/top-products?limit=10${categoryId ? `&category_id=${categoryId}` : ''}`);
          setTop10(topTen);

        } else {
          setProducts([]);
          setMonthlySeries({ months: [], series: [] });
          setTop10([]);
        }
      } catch (e) { console.error(e); }
    })();
  }, [categoryId, productId]);

  // Initial loads for transactions/metrics + always-visible analytics (top sales, top outlets)
  React.useEffect(() => {
    (async () => {
      setLoading(true);
      try {
        const [resTx, resMetrics, topS, outlets] = await Promise.all([
          apiFetch('/api/transactions'),
          apiFetch('/api/dashboard/metrics'),
          apiFetch('/api/analytics/top-sales'),
          apiFetch('/api/analytics/top-outlets'),
        ]);
        const items: any[] = Array.isArray(resTx)
          ? resTx
          : Array.isArray(resTx?.data)
            ? resTx.data
            : Array.isArray(resTx?.data?.data)
              ? resTx.data.data
              : Array.isArray(resTx?.items)
                ? resTx.items
                : [];
        setTransactions(items);
        setMetrics(resMetrics);
        setTopSales(topS);
        setTopOutlets(outlets);
      } catch (e) { console.error(e); }
      finally { setLoading(false); }
    })();
  }, []);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0
    }).format(amount);
  };

  // Helpers: date range by filter and filtering function
  const getDateRange = (filter: string) => {
    const now = new Date();
    const start = new Date(now);
    const end = new Date(now);
    // Normalize to start/end of day
    const startOfDay = (d: Date) => { const x = new Date(d); x.setHours(0, 0, 0, 0); return x; };
    const endOfDay = (d: Date) => { const x = new Date(d); x.setHours(23, 59, 59, 999); return x; };

    switch (filter) {
      case 'today':
        return { start: startOfDay(now), end: endOfDay(now) };
      case 'this-week': {
        const day = now.getDay() || 7; // 1-7 (Mon=1 if we shift), but JS Sun=0
        const monday = new Date(now);
        monday.setDate(now.getDate() - ((day === 0 ? 7 : day) - 1));
        return { start: startOfDay(monday), end: endOfDay(now) };
      }
      case 'last-month': {
        const firstDayPrev = new Date(now.getFullYear(), now.getMonth() - 1, 1);
        const lastDayPrev = new Date(now.getFullYear(), now.getMonth(), 0);
        return { start: startOfDay(firstDayPrev), end: endOfDay(lastDayPrev) };
      }
      case 'this-year': {
        const first = new Date(now.getFullYear(), 0, 1);
        return { start: startOfDay(first), end: endOfDay(now) };
      }
      case 'this-month':
      default: {
        const first = new Date(now.getFullYear(), now.getMonth(), 1);
        return { start: startOfDay(first), end: endOfDay(now) };
      }
    }
  };

  const filteredTransactions = React.useMemo(() => {
    const { start, end } = getDateRange(dateFilter);
    return (transactions || []).filter((t: any) => {
      const created = new Date(t.created_at ?? t.createdAt ?? t.date ?? Date.now());
      return created >= start && created <= end;
    });
  }, [transactions, dateFilter]);

  // Derived metrics from filtered data
  const totalSales = filteredTransactions.reduce((sum, t: any) => sum + Number(t.total || 0), 0);
  const totalTransactions = filteredTransactions.length;
  const completedTransactions = filteredTransactions.filter((t: any) => t.status === 'delivered').length;
  const pendingTransactions = filteredTransactions.filter((t: any) => ['pending_owner', 'pending_gudang'].includes(t.status)).length;

  // Compute Top Products & Mitras from filtered transactions; fallback to metrics if empty
  const computedTopProducts = React.useMemo(() => {
    const map: Record<string, { name: string; sold: number; revenue: number }> = {};
    for (const t of filteredTransactions) {
      const items = Array.isArray(t.items) ? t.items : [];
      for (const i of items) {
        const name = i.product?.name ?? i.product_name ?? (i.product_id ? `Produk #${i.product_id}` : 'Produk Tidak Diketahui');
        if (!map[name]) map[name] = { name, sold: 0, revenue: 0 };
        map[name].sold += Number(i.quantity || 0);
        map[name].revenue += Number(i.total ?? (Number(i.price || 0) * Number(i.quantity || 0) - Number(i.discount || 0)));
      }
    }
    return Object.values(map).sort((a, b) => b.revenue - a.revenue).slice(0, 5);
  }, [filteredTransactions]);

  const computedTopMitras = React.useMemo(() => {
    const map: Record<string, { name: string; transactions: number; revenue: number }> = {};
    for (const t of filteredTransactions) {
      const name = t.mitra?.name ?? t.mitraName ?? (t.mitra_id ? `Mitra #${t.mitra_id}` : 'Mitra');
      if (!map[name]) map[name] = { name, transactions: 0, revenue: 0 };
      map[name].transactions += 1;
      map[name].revenue += Number(t.total || 0);
    }
    return Object.values(map).sort((a, b) => b.revenue - a.revenue).slice(0, 5);
  }, [filteredTransactions]);

  const monthlyData = (metrics?.monthly_sales ?? []).map((m: any) => ({ month: String(m.month).padStart(2, '0'), sales: Number(m.total), transactions: Number(m.cnt ?? 0) }));
  const maxSales = Math.max(1, ...monthlyData.map(d => d.sales));
  const topProducts = computedTopProducts.length ? computedTopProducts : (metrics?.top_products ?? []).map((p: any) => ({ name: p.product?.name ?? (p.product_id ? `Produk #${p.product_id}` : 'Produk Tidak Diketahui'), sold: p.qty, revenue: Number(p.revenue ?? 0) }));
  const topMitras = computedTopMitras.length ? computedTopMitras : (metrics?.top_mitras ?? []).map((m) => ({ name: m.name, transactions: m.cnt ?? 0, revenue: m.total }));

  // Compute month-over-month trends from DB monthly_sales (for cards below)
  const monthSalesMap = Object.fromEntries(monthlyData.map((d: any) => [Number(d.month), Number(d.sales)]));
  const monthTxMap = Object.fromEntries(monthlyData.map((d: any) => [Number(d.month), Number(d.transactions)]));
  const curMonth = new Date().getMonth() + 1;
  const prevMonth = curMonth === 1 ? 12 : curMonth - 1;
  const salesCur = monthSalesMap[curMonth] ?? 0;
  const salesPrev = monthSalesMap[prevMonth] ?? 0;
  const txCur = monthTxMap[curMonth] ?? 0;
  const txPrev = monthTxMap[prevMonth] ?? 0;
  const salesMoM: number | null = salesPrev > 0 ? Math.round(((salesCur - salesPrev) / salesPrev) * 100) : null;
  const txMoM: number | null = txPrev > 0 ? Math.round(((txCur - txPrev) / txPrev) * 100) : null;


  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-3">
        <h1 className="text-3xl font-bold text-gray-900">Laporan & Analytics</h1>
        <div className="flex items-center flex-wrap gap-3">
          {/* Filter Kategori & Produk */}
          <select
            value={categoryId}
            onChange={(e) => { setCategoryId(e.target.value); setProductId(''); }}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500"
          >
            <option value="">Pilih Kategori</option>
            {categories.map((c) => <option key={c.id} value={c.id}>{c.name}</option>)}
          </select>
          <select
            value={productId}
            onChange={(e) => setProductId(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500 disabled:bg-gray-100"
            disabled={!categoryId}
          >
            <option value="">Semua Produk</option>
            {products.map((p) => <option key={p.id} value={p.id}>{p.name}</option>)}
          </select>

          <button onClick={() => {
            const rows = transactions.map((t: any) => ({
              id: t.id,
              mitra: t.mitra?.name ?? t.mitraName ?? '',
              total: t.total,
              status: t.status,
              tanggal: t.created_at ?? t.createdAt ?? ''
            }));
            const header = 'ID,Mitra,Total,Status,Tanggal';
            const csv = [header, ...rows.map(r => {
              const mitra = String(r.mitra).split('"').join('""');
              return `${r.id},"${mitra}",${r.total},${r.status},${r.tanggal}`;
            })].join('\n');
            const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
            const url = URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = 'laporan-transaksi.csv';
            link.click();
            URL.revokeObjectURL(url);
          }} className="flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
            <Download size={20} className="mr-2" />
            Export CSV
          </button>
          <button onClick={async () => {
            await exportOutletsAndHistoryToPdf();
          }} className="flex items-center px-4 py-2 bg-white text-gray-700 border rounded-lg hover:bg-gray-50">
            Export PDF
          </button>
        </div>
      </div>

      {/* Summary & Analytics grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6 items-stretch">
        {/* Analytics Charts (4 cards row) */}
        {categoryId ? (
          <>
            {/* Monthly Line Chart */}
            <div id="monthly-line" className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 col-span-1 xl:col-span-2">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Trend Penjualan per Bulan</h3>
              <Chart
                type="line"
                height={320}
                series={(monthlySeries.series || []).map((s: any) => ({ name: s.name, data: s.qty }))}
                options={{
                  chart: { id: 'monthlyQty' },
                  xaxis: { categories: (monthlySeries.months || []).map((m: number) => String(m).padStart(2, '0')) },
                  yaxis: { title: { text: 'Qty' } },
                  stroke: { curve: 'smooth' },
                  tooltip: { shared: true },
                }}
              />
            </div>

            {/* Top 10 Products Pie */}
            <div id="top10-pie" className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 col-span-1 xl:col-span-2">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Top 10 Produk Terlaris</h3>
              <Chart
                type="pie"
                height={320}
                series={top10.map((r: any) => Number(r.qty))}
                options={{
                  labels: top10.map((r: any) => r.name),
                  dataLabels: { enabled: true, formatter: (val: number) => `${val.toFixed(1)}%` },
                  tooltip: { y: { formatter: (val: number) => `${val} qty` } },
                  legend: { position: 'bottom' }
                }}
              />
            </div>
          </>
        ) : (
          <div className="col-span-1 md:col-span-2 xl:col-span-4 bg-white p-6 rounded-xl shadow-sm border border-gray-100 text-sm text-gray-600">
            Pilih kategori untuk menampilkan grafik penjualan dan produk terlaris.
          </div>
        )}

        {/* Top Sales by Revenue */}
        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 col-span-1 xl:col-span-2">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Sales Terbaik (Berdasarkan Omset)</h3>
          <Chart
            type="bar"
            height={320}
            series={[{ name: 'Omset', data: topSales.map((r: any) => Number(r.revenue)) }]}
            options={{
              xaxis: { categories: topSales.map((r: any) => r.name) },
              dataLabels: { enabled: false },
              tooltip: { y: { formatter: (v: number) => formatCurrency(v) } },
            }}
          />
        </div>

        {/* Top Outlets Table */}
        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 col-span-1 xl:col-span-2">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Outlet dengan Nominal Pembelian Terbesar</h3>
            <button
              onClick={async () => {
                await exportOutletsAndHistoryToPdf();
              }}
              className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
            >
              Export PDF
            </button>
          </div>
          <div id="reports-root">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Outlet</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Jumlah Transaksi</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Nominal</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {topOutlets.map((o: any) => (
                    <tr key={o.mitra_id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{o.name}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{o.cnt}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{formatCurrency(Number(o.total))}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Modal detail outlet */}
            {selectedOutlet && (
              <div className="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50">
                <div className="bg-white rounded-xl shadow-lg p-6 w-full max-w-3xl">
                  <div className="flex items-center justify-between mb-4">
                    <h4 className="text-lg font-semibold">Detail Produk – {selectedOutlet.name}</h4>
                    <button className="text-gray-500" onClick={() => { setSelectedOutlet(null); setOutletProducts([]); }}>Tutup</button>
                  </div>
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Produk</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Qty</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Revenue</th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {outletProducts.map((r: any) => (
                          <tr key={r.product_id}>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{r.name}</td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{r.qty}</td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{formatCurrency(Number(r.revenue))}</td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Penjualan</p>
              <p className="text-2xl font-bold text-gray-900 mt-1 break-all">{formatCurrency(totalSales)}</p>
              {typeof salesMoM === 'number' && (
                <p className={`text-sm mt-1 ${salesMoM >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                  {salesMoM > 0 ? `+${salesMoM}%` : `${salesMoM}%`} dari bulan lalu
                </p>
              )}


            </div>
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center flex-shrink-0">
              <TrendingUp size={24} className="text-green-600" />
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Transaksi</p>
              <p className="text-2xl font-bold text-gray-900 mt-1">{totalTransactions}</p>
              {typeof txMoM === 'number' && (
                <p className={`text-sm mt-1 ${txMoM >= 0 ? 'text-blue-600' : 'text-red-600'}`}>
                  {txMoM > 0 ? `+${txMoM}%` : `${txMoM}%`} dari bulan lalu
                </p>
              )}
            </div>
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <BarChart3 size={24} className="text-blue-600" />
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Transaksi Selesai</p>
              <p className="text-2xl font-bold text-gray-900 mt-1">{completedTransactions}</p>
              {filteredTransactions.length > 0 && (
                <p className="text-sm text-purple-600 mt-1">Rate: {Math.round((completedTransactions / filteredTransactions.length) * 100)}%</p>
              )}
            </div>
            <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
              <Package size={24} className="text-purple-600" />
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Pending Approval</p>
              <p className="text-2xl font-bold text-gray-900 mt-1">{pendingTransactions}</p>
              <p className="text-sm text-orange-600 mt-1">Perlu tindak lanjut</p>
            </div>
            <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
              <Calendar size={24} className="text-orange-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Charts and Tables */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Monthly Sales Chart */}
        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Penjualan Bulanan</h3>
          <div className="space-y-4">
            {monthlyData.map((data) => (
              <div key={data.month} className="flex items-center w-full">
                <div className="flex items-center w-full">
                  <span className="text-sm font-medium text-gray-600 w-8">{data.month}</span>
                  <div className="ml-4 w-full">
                    <div className="flex justify-between items-center mb-1">
                      <span className="text-sm text-gray-900">{formatCurrency(data.sales)}</span>
                      <span className="text-xs text-gray-500">{data.transactions} transaksi</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div className="bg-green-500 h-2 rounded-full w-full" style={{ width: '100%' }}></div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Top Products */}
        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Produk Terlaris</h3>
          <div className="space-y-4">
            {topProducts.map((product, index) => (
              <div key={product.name} className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center mr-3">
                    <span className="text-sm font-medium text-green-600">#{index + 1}</span>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-900">{product.name}</p>
                    <p className="text-xs text-gray-500">{product.sold} terjual</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-sm font-medium text-gray-900">{formatCurrency(product.revenue)}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Top Mitras */}
      <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Mitra Terbaik</h3>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Ranking
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Nama Mitra
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Jumlah Transaksi
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Total Revenue
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Rata-rata per Transaksi
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {topMitras.map((mitra, index) => (
                <tr key={mitra.name} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                      <span className="text-sm font-medium text-green-600">#{index + 1}</span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">{mitra.name}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{mitra.transactions}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">{formatCurrency(mitra.revenue)}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">
                      {formatCurrency(mitra.revenue / mitra.transactions)}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Riwayat Transaksi (dipindah ke bawah, di luar card Total Penjualan) */}
      <div id="tx-history-root" className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Riwayat Transaksi</h3>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tanggal</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Mitra</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sales</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredTransactions.slice(0, 50).map((t: any) => (
                <tr key={t.id}>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{String(t.created_at ?? t.createdAt ?? '').slice(0, 10)}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{t.mitra?.name ?? t.mitraName ?? '-'}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{t.sales?.name ?? t.salesName ?? '-'}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 capitalize">{(t.status ?? '').replace('_', ' ')}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{formatCurrency(Number(t.total || 0))}</td>
                </tr>
              ))}
            </tbody>
          </table>
          {filteredTransactions.length > 50 && (
            <p className="text-xs text-gray-500 mt-2">Menampilkan 50 transaksi terbaru di PDF. Gunakan CSV untuk data lengkap.</p>
          )}
        </div>
      </div>

    </div>


  );
};

export default Reports;