<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;

class UserController extends Controller
{
    public function index(Request $request)
    {
        $query = User::query();
        // Owner can only see their own admin_gudang and sales
        if ($request->user() && $request->user()->role === 'owner') {
            $query->where(function ($q) use ($request) {
                $q->where('id', $request->user()->id)
                    ->orWhere(function ($q2) use ($request) {
                        $q2->whereIn('role', ['admin_gudang', 'sales'])
                            ->where('owner_id', $request->user()->id);
                    });
            });
        }
        return $query->latest()->paginate(20);
    }

    public function store(Request $request)
    {
        $rules = [
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'email', 'unique:users,email'],
            'phone' => ['required', 'string', 'max:50'],
            'role' => ['required', 'in:superadmin,owner,admin_gudang,sales'],
            'password' => ['required', 'string', 'min:6'],
        ];
        // Superadmin can choose owner for admin_gudang/sales
        if (in_array($request->input('role'), ['admin_gudang', 'sales'])) {
            $rules['owner_id'] = ['nullable', 'integer', 'exists:users,id'];
        }
        $data = $request->validate($rules);
        $data['password'] = Hash::make($data['password']);

        // When owner creates admin_gudang/sales, force assign to that owner
        if ($request->user() && $request->user()->role === 'owner' && in_array($data['role'], ['admin_gudang', 'sales'])) {
            $data['owner_id'] = $request->user()->id;
        }
        // If creating an owner/superadmin, ensure owner_id is null
        if (in_array($data['role'], ['owner', 'superadmin'])) {
            $data['owner_id'] = null;
        }

        $user = User::create($data);
        return response()->json($user, 201);
    }

    public function update(Request $request, User $user)
    {
        // Allow superadmin to change owner_id for sales/admin_gudang. Owners cannot reassign.
        $rules = [
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'email', 'unique:users,email,' . $user->id],
            'phone' => ['required', 'string', 'max:50'],
            'role' => ['required', 'in:superadmin,owner,admin_gudang,sales'],
            'password' => ['nullable', 'string', 'min:6'],
            'owner_id' => ['nullable', 'integer', 'exists:users,id'],
        ];

        $data = $request->validate($rules);

        if (!empty($data['password'])) {
            $data['password'] = Hash::make($data['password']);
        } else {
            unset($data['password']);
        }

        // Determine target role (either provided or existing)
        $targetRole = $data['role'] ?? $user->role;

        // Enforce owner_id logic by actor role and target role
        if ($request->user() && $request->user()->role === 'owner') {
            // Owner can only assign their own id to their team
            if (in_array($targetRole, ['admin_gudang', 'sales'])) {
                $data['owner_id'] = $request->user()->id;
            } else {
                $data['owner_id'] = null;
            }
        } else {
            // Superadmin (or other elevated roles) can set owner_id for sales/admin_gudang, otherwise null
            if (in_array($targetRole, ['admin_gudang', 'sales'])) {
                // Keep provided owner_id (can be null to detach). If not provided, keep existing owner_id.
                if ($request->exists('owner_id')) {
                    $data['owner_id'] = $request->input('owner_id');
                } else {
                    $data['owner_id'] = $user->owner_id; // do not accidentally nullify
                }
            } else {
                $data['owner_id'] = null;
            }
        }

        $user->update($data);
        return response()->json($user);
    }

    public function destroy(User $user)
    {
        $user->delete();
        return response()->json(['ok' => true]);
    }
}
