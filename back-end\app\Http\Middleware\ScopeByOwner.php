<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class ScopeByOwner
{
    public function handle(Request $request, Closure $next): Response
    {
        $user = $request->user();
        if ($user && $user->role === 'owner') {
            // Inject owner_id to request for controllers to use
            $request->attributes->set('owner_scope_id', $user->id);
        }
        return $next($request);
    }
}

