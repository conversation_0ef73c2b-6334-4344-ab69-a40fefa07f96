import React, { useEffect, useState } from 'react';
import { useAuth } from '../context/AuthContext';
import { apiFetch } from '../services/api';
import {
  Users,
  Package,
  ShoppingCart,
  TrendingUp,
  AlertTriangle,
  Clock,
  CheckCircle,
  XCircle
} from 'lucide-react';

type Activity = { type: string; message: string; at: string };
type MonthlySalesEntry = { month: number; total: number };
type TopProduct = { product_id: number; qty: number; product?: { id: number; name: string } };
type TopMitra = { mitra_id: number; name: string; total: number; cnt?: number };

type Metrics = {
  total: { users: number; mitras: number; products: number; transactions: number };
  pending: { owner: number; gudang: number };
  counts_by_status: Record<string, number>;
  shipments: { shipped_today: number; delivered_today: number };
  sales: {
    today: number; week: number; month: number; yesterday?: number;
    revenue_total?: number; revenue_cash?: number; receivable_outstanding?: number;
    trends?: { sales_today_vs_yesterday?: number; approved_this_month_vs_last_month?: number };
  };
  period: { transactions_today: number; transactions_week: number; transactions_month: number; trends?: { transactions_month_vs_last_month?: number } };
  low_stock_count: number;
  activities?: Activity[];
  monthly_sales?: MonthlySalesEntry[];
  top_products?: TopProduct[];
  top_mitras?: TopMitra[];
  pending_lists?: { owner: any[]; gudang: any[] };
  low_stock_list?: { id: number; name: string; stock: number }[];
  recent_transactions?: any[];
};

const Dashboard: React.FC = () => {
  const { user } = useAuth();
  const [metrics, setMetrics] = useState<Metrics | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    let isMounted = true;
    setLoading(true);
    apiFetch('/api/dashboard/metrics')
      .then((data) => { if (isMounted) { setMetrics(data); setError(null); } })
      .catch((e) => { if (isMounted) setError(e.message || 'Gagal memuat dashboard'); })
      .finally(() => { if (isMounted) setLoading(false); });
    return () => { isMounted = false; };
  }, []);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0
    }).format(amount || 0);
  };


  // Konsistenkan label status dengan halaman "Transaksi Saya"
  const getStatusLabel = (status: string) => {
    const labels: Record<string, string> = {
      'pending_owner': 'Menunggu Persetujuan Owner',
      'pending_gudang': 'Menunggu Persetujuan Gudang',
      'approved': 'Disetujui - Siap Kirim',
      'rejected': 'Ditolak',
      'shipped': 'Sedang Dikirim',
      'delivered': 'Sudah Diterima'
    };
    return labels[status] || status;
  };

  const StatCard: React.FC<{
    title: string;
    value: string | number;
    icon: React.ElementType;
    color: string;
    trend?: string;
  }> = ({ title, value, icon: Icon, color, trend }) => (
    <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-shadow">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className="text-2xl font-bold text-gray-900 mt-1">{value}</p>
          {trend && (
            <p className="text-sm text-green-600 mt-1">{trend}</p>
          )}
        </div>
        <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${color}`}>
          <Icon size={24} className="text-white" />
        </div>
      </div>
    </div>
  );

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {Array.from({ length: 4 }).map((_, idx) => (
            <div key={idx} className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
              <div className="h-4 bg-gray-200 rounded w-1/3 mb-3"></div>
              <div className="h-6 bg-gray-200 rounded w-1/2"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 text-red-800 p-4 rounded">
        Gagal memuat dashboard: {error}
      </div>
    );
  }

  if (!metrics) return null;


  if (user?.role === 'superadmin') {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold text-gray-900">Super Admin Dashboard</h1>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <StatCard title="Total Users" value={metrics.total.users} icon={Users} color="bg-blue-500" />
          <StatCard title="Total Mitra" value={metrics.total.mitras} icon={Users} color="bg-green-500" />
          <StatCard title="Total Produk" value={metrics.total.products} icon={Package} color="bg-purple-500" />
          <StatCard title="Total Transaksi" value={metrics.total.transactions} icon={ShoppingCart} color="bg-orange-500" />
        </div>

        {/* Revenue cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <StatCard title="Omset Keseluruhan" value={formatCurrency(metrics.sales?.revenue_total || 0)} icon={TrendingUp} color="bg-green-500" />
          <StatCard title="Omset Piutang" value={formatCurrency(metrics.sales?.receivable_outstanding || 0)} icon={AlertTriangle} color="bg-yellow-500" />
          <StatCard title="Omset Lunas" value={formatCurrency(metrics.sales?.revenue_cash || 0)} icon={CheckCircle} color="bg-blue-500" />
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Aktivitas Sistem</h3>
            <div className="space-y-3">
              {metrics.activities?.map((a, idx) => (
                <div key={idx} className="flex items-center justify-between py-3 border-b border-gray-100">
                  <div className="flex items-center">
                    <div className={`w-2 h-2 ${a.type === 'stock' ? 'bg-orange-500' : 'bg-blue-500'} rounded-full mr-3`}></div>
                    <span className="text-sm text-gray-600">{a.message}</span>
                  </div>
                  <span className="text-xs text-gray-400">{new Date(a.at).toLocaleString('id-ID')}</span>
                </div>
              ))}
              {!metrics.activities?.length && (
                <div className="text-sm text-gray-500">Belum ada aktivitas terbaru</div>
              )}
            </div>
          </div>

          <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Performa Penjualan</h3>
            <div className="space-y-4">
              <div className="flex justify-between text-sm mb-2"><span className="text-gray-600">Hari ini</span><span className="font-medium">{formatCurrency(metrics.sales.today)}</span></div>
              <div className="flex justify-between text-sm mb-2"><span className="text-gray-600">Minggu ini</span><span className="font-medium">{formatCurrency(metrics.sales.week)}</span></div>
              <div className="flex justify-between text-sm mb-2"><span className="text-gray-600">Bulan ini</span><span className="font-medium">{formatCurrency(metrics.sales.month)}</span></div>
            </div>
            <div className="mt-4">
              <h4 className="text-sm font-semibold text-gray-800 mb-2">Penjualan Bulanan</h4>
              <div className="space-y-2">
                {metrics.monthly_sales?.map((m) => (
                  <div key={m.month} className="flex items-center justify-between">
                    <span className="text-sm text-gray-600 w-10">{m.month}</span>
                    <div className="flex-1 mx-3">
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div className="bg-green-500 h-2 rounded-full" style={{ width: `${Math.min(100, (m.total / (metrics.sales.month || 1)) * 100)}%` }}></div>
                      </div>
                    </div>
                    <span className="text-sm font-medium">{formatCurrency(m.total)}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (user?.role === 'owner') {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold text-gray-900">Owner Dashboard</h1>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <StatCard
            title="Pendapatan Hari Ini"
            value={formatCurrency(metrics.sales.today)}
            icon={TrendingUp}
            color="bg-green-500"
            trend={typeof metrics.sales?.trends?.sales_today_vs_yesterday === 'number' ? `${metrics.sales.trends.sales_today_vs_yesterday > 0 ? '+' : ''}${metrics.sales.trends.sales_today_vs_yesterday}% dari kemarin` : undefined}
          />
          <StatCard
            title="Pending Approval"
            value={metrics.pending.owner}
            icon={Clock}
            color="bg-orange-500"
          />
          <StatCard
            title="Transaksi Disetujui"
            value={metrics.counts_by_status.approved || 0}
            icon={CheckCircle}
            color="bg-blue-500"
            trend={typeof metrics.sales?.trends?.approved_this_month_vs_last_month === 'number' ? `${metrics.sales.trends.approved_this_month_vs_last_month > 0 ? '+' : ''}${metrics.sales.trends.approved_this_month_vs_last_month}% dari bulan lalu` : undefined}
          />
          <StatCard
            title="Transaksi Ditolak"
            value={metrics.counts_by_status.rejected || 0}
            icon={XCircle}
            color="bg-red-500"
          />
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Pengajuan Diskon Pending</h3>
            <div className="space-y-3">
              {metrics.pending_lists?.owner?.map((t: any) => (
                <div key={t.id} className="flex items-center justify-between p-3 bg-orange-50 rounded-lg">
                  <div>
                    <p className="font-medium text-gray-900">TXN-{t.id}</p>
                    <p className="text-sm text-gray-600">{t.mitra_name || 'Mitra'} - {formatCurrency(t.total)}</p>
                  </div>
                </div>
              ))}
              {!metrics.pending_lists?.owner?.length && (
                <div className="text-sm text-gray-500">Tidak ada pengajuan diskon pending</div>
              )}
            </div>
          </div>

          <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Monitoring Real-time</h3>
            <div className="space-y-3 text-sm">
              <div className="flex items-center justify-between py-2">
                <span className="text-gray-600">Transaksi Hari Ini</span>
                <span className="font-medium">{metrics.period.transactions_today} transaksi</span>
              </div>
              <div className="flex items-center justify-between py-2">
                <span className="text-gray-600">Persetujuan Tertunda</span>
                <span className="font-medium text-orange-600">{(metrics.counts_by_status.pending_owner || 0) + (metrics.counts_by_status.pending_gudang || 0)} transaksi</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (user?.role === 'admin_gudang') {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold text-gray-900">Admin Gudang Dashboard</h1>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <StatCard
            title="Total Produk"
            value={metrics.total.products}
            icon={Package}
            color="bg-blue-500"
          />
          <StatCard
            title="Stok Rendah"
            value={metrics.low_stock_count}
            icon={AlertTriangle}
            color="bg-red-500"
          />
          <StatCard
            title="Pending Approval"
            value={metrics.pending.gudang}
            icon={Clock}
            color="bg-orange-500"
          />
          <StatCard
            title="Terkirim Hari Ini"
            value={metrics.shipments.shipped_today}
            icon={CheckCircle}
            color="bg-green-500"
          />
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Transaksi Pending Approval</h3>
            <div className="space-y-3">
              {metrics.pending_lists?.gudang?.map((t: any) => (
                <div key={t.id} className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                  <div>
                    <p className="font-medium text-gray-900">TXN-{t.id}</p>
                    <p className="text-sm text-gray-600">{t.mitra_name || 'Mitra'} - {formatCurrency(t.total)}</p>
                  </div>
                </div>
              ))}
              {!metrics.pending_lists?.gudang?.length && (
                <div className="text-sm text-gray-500">Tidak ada transaksi pending approval</div>
              )}
            </div>
          </div>

          <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Alert Stok Rendah</h3>
            <div className="space-y-3">
              {metrics.low_stock_list?.map((p) => (
                <div key={p.id} className="flex items-center justify-between py-2">
                  <div>
                    <p className="font-medium text-gray-900">{p.name}</p>
                    <p className="text-sm text-red-600">Stok: {p.stock}</p>
                  </div>
                </div>
              ))}
              {!metrics.low_stock_list?.length && (
                <div className="text-sm text-gray-500">Tidak ada produk dengan stok rendah</div>
              )}
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (user?.role === 'sales') {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold text-gray-900">Sales Dashboard</h1>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <StatCard
            title="Mitra Aktif"
            value={metrics.total.mitras}
            icon={Users}
            color="bg-green-500"
          />
          <StatCard
            title="Transaksi Bulan Ini"
            value={metrics.period.transactions_month}
            icon={ShoppingCart}
            color="bg-blue-500"
            trend={typeof metrics.period?.trends?.transactions_month_vs_last_month === 'number' ? `${metrics.period.trends.transactions_month_vs_last_month > 0 ? '+' : ''}${metrics.period.trends.transactions_month_vs_last_month}% dari bulan lalu` : undefined}
          />
          <StatCard
            title="Pending Approval"
            value={(metrics.counts_by_status.pending_owner || 0) + (metrics.counts_by_status.pending_gudang || 0)}
            icon={Clock}
            color="bg-orange-500"
          />
          <StatCard
            title="Total Penjualan"
            value={formatCurrency(metrics.sales.month)}
            icon={TrendingUp}
            color="bg-purple-500"
            trend={typeof metrics.sales?.trends?.approved_this_month_vs_last_month === 'number' ? `${metrics.sales.trends.approved_this_month_vs_last_month > 0 ? '+' : ''}${metrics.sales.trends.approved_this_month_vs_last_month}% dari bulan lalu` : undefined}
          />
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Transaksi Terbaru</h3>
            <div className="space-y-3">
              {metrics.recent_transactions?.map((t: any) => (
                <div key={t.id} className="flex items-center justify-between p-3 rounded-lg" style={{ backgroundColor: t.status === 'delivered' ? '#ecfdf5' : t.status === 'shipped' ? '#eff6ff' : '#f9fafb' }}>
                  <div>
                    <p className="font-medium text-gray-900">TXN-{t.id}</p>
                    <p className="text-sm text-gray-600">{t.mitra_name || 'Mitra'} - {formatCurrency(t.total)}</p>
                  </div>
                  <span className="px-2 py-1 rounded text-xs font-medium" style={{ backgroundColor: '#eef2ff', color: '#3730a3' }}>
                    {getStatusLabel(t.status)}
                  </span>
                </div>
              ))}
              {!metrics.recent_transactions?.length && (
                <div className="text-sm text-gray-500">Belum ada transaksi terbaru</div>
              )}
            </div>
          </div>

          <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Top Mitra</h3>
            <div className="space-y-3">
              {metrics.top_mitras?.map((m: any, idx: number) => (
                <div key={m.mitra_id} className="flex items-center justify-between py-2">
                  <div>
                    <p className="text-sm text-gray-500">#{idx + 1}</p>
                    <p className="font-medium text-gray-900">{m.name}</p>
                    {m.cnt != null && <p className="text-sm text-gray-600">{m.cnt} transaksi</p>}
                  </div>
                  <span className="font-medium text-green-600">{formatCurrency(m.total)}</span>
                </div>
              ))}
              {!metrics.top_mitras?.length && (
                <div className="text-sm text-gray-500">Belum ada data mitra</div>
              )}
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="text-center py-12">
      <h2 className="text-2xl font-bold text-gray-900">Welcome to Agricultural Cashier System</h2>
      <p className="text-gray-600 mt-2">Dashboard content will be displayed here based on your role.</p>
    </div>
  );
};

export default Dashboard;