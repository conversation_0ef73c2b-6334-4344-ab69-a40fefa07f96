<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class AnalyticsController extends Controller
{
    private array $salesStatuses = ['approved', 'shipped', 'delivered'];

    // GET /api/analytics/products/monthly?category_id=..&product_id=..&year=2025&limit=5
    public function productMonthly(Request $request)
    {
        $request->validate([
            'category_id' => ['required', 'integer'],
            'product_id' => ['nullable', 'integer'],
            'year' => ['nullable', 'integer'],
            'limit' => ['nullable', 'integer']
        ]);

        $categoryId = (int) $request->query('category_id');
        $productId = $request->query('product_id');
        $year = (int) ($request->query('year') ?? Carbon::now()->year);
        $limit = (int) ($request->query('limit') ?? 5);

        // Base transaction items in given year and statuses
        $base = DB::table('transaction_items as ti')
            ->join('transactions as t', 'ti.transaction_id', '=', 't.id')
            ->join('products as p', 'ti.product_id', '=', 'p.id')
            ->select(
                DB::raw('MONTH(t.created_at) as month'),
                'ti.product_id',
                'p.name as product_name',
                DB::raw('SUM(ti.quantity) as qty'),
                DB::raw('SUM(ti.total) as revenue')
            )
            ->whereYear('t.created_at', $year)
            ->whereIn('t.status', $this->salesStatuses)
            ->where('p.category_id', $categoryId)
            ->groupBy(DB::raw('MONTH(t.created_at)'), 'ti.product_id', 'p.name');

        $series = [];
        $months = range(1, 12);

        if ($productId) {
            $rows = (clone $base)->where('ti.product_id', $productId)->get();
            $map = array_fill_keys($months, ['qty' => 0, 'revenue' => 0]);
            foreach ($rows as $r) {
                $map[(int)$r->month] = ['qty' => (int)$r->qty, 'revenue' => (int)$r->revenue];
            }
            $productName = $rows[0]->product_name ?? (DB::table('products')->where('id', $productId)->value('name') ?? 'Produk');
            $series[] = [
                'product_id' => (int)$productId,
                'name' => $productName,
                'qty' => array_values(array_column($map, 'qty')),
                'revenue' => array_values(array_column($map, 'revenue')),
            ];
        } else {
            // Find top N products by qty in the year within category
            $top = DB::table('transaction_items as ti')
                ->join('transactions as t', 'ti.transaction_id', '=', 't.id')
                ->join('products as p', 'ti.product_id', '=', 'p.id')
                ->select('ti.product_id', 'p.name', DB::raw('SUM(ti.quantity) as qty'))
                ->whereYear('t.created_at', $year)
                ->whereIn('t.status', $this->salesStatuses)
                ->where('p.category_id', $categoryId)
                ->groupBy('ti.product_id', 'p.name')
                ->orderByDesc(DB::raw('SUM(ti.quantity)'))
                ->limit($limit)
                ->get();

            foreach ($top as $prod) {
                $rows = (clone $base)->where('ti.product_id', $prod->product_id)->get();
                $map = array_fill_keys($months, ['qty' => 0, 'revenue' => 0]);
                foreach ($rows as $r) {
                    $map[(int)$r->month] = ['qty' => (int)$r->qty, 'revenue' => (int)$r->revenue];
                }
                $series[] = [
                    'product_id' => (int)$prod->product_id,
                    'name' => $prod->name,
                    'qty' => array_values(array_column($map, 'qty')),
                    'revenue' => array_values(array_column($map, 'revenue')),
                ];
            }
        }

        return response()->json([
            'year' => $year,
            'months' => $months,
            'series' => $series,
        ]);
    }

    // GET /api/analytics/top-products?category_id=&year=&limit=10
    public function topProducts(Request $request)
    {
        $categoryId = $request->query('category_id');
        $year = (int) ($request->query('year') ?? Carbon::now()->year);
        $limit = (int) ($request->query('limit') ?? 10);

        $q = DB::table('transaction_items as ti')
            ->join('transactions as t', 'ti.transaction_id', '=', 't.id')
            ->join('products as p', 'ti.product_id', '=', 'p.id')
            ->select('ti.product_id', 'p.name', DB::raw('SUM(ti.quantity) as qty'), DB::raw('SUM(ti.total) as revenue'))
            ->whereYear('t.created_at', $year)
            ->whereIn('t.status', $this->salesStatuses)
            ->groupBy('ti.product_id', 'p.name')
            ->orderByDesc(DB::raw('SUM(ti.quantity)'));
        if ($categoryId) {
            $q->where('p.category_id', (int)$categoryId);
        }
        $rows = $q->limit($limit)->get();

        return response()->json($rows);
    }

    // GET /api/analytics/top-sales?year=&limit=10
    public function topSales(Request $request)
    {
        $year = (int) ($request->query('year') ?? Carbon::now()->year);
        $limit = (int) ($request->query('limit') ?? 10);
        $rows = DB::table('transactions as t')
            ->join('users as u', 't.sales_id', '=', 'u.id')
            ->leftJoin('users as owner', 'u.owner_id', '=', 'owner.id')
            ->select('u.id as user_id', 'u.name', DB::raw('SUM(t.total) as revenue'), DB::raw('COUNT(*) as cnt'), 'owner.id as owner_id')
            ->whereYear('t.created_at', $year)
            ->whereIn('t.status', $this->salesStatuses)
            ->when(($request->user() && $request->user()->role === 'owner'), function ($q) use ($request) {
                $q->where(function ($w) use ($request) {
                    $w->where('u.owner_id', $request->user()->id)->orWhere('u.id', $request->user()->id);
                });
            })
            ->groupBy('u.id', 'u.name', 'owner.id')
            ->orderByDesc(DB::raw('SUM(t.total)'))
            ->limit($limit)
            ->get();
        return response()->json($rows);
    }

    // GET /api/analytics/top-outlets?year=&limit=10
    public function topOutlets(Request $request)
    {
        $year = (int) ($request->query('year') ?? Carbon::now()->year);
        $limit = (int) ($request->query('limit') ?? 10);

        $user = $request->user();
        $role = $user?->role;
        $ownerScopeId = null;
        if ($user && $role !== 'superadmin') {
            $ownerScopeId = $role === 'owner' ? $user->id : ($role === 'admin_gudang' || $role === 'sales' ? $user->owner_id : null);
        }

        $rows = DB::table('transactions as t')
            ->join('mitras as m', 't.mitra_id', '=', 'm.id')
            ->leftJoin('users as creator', 'm.created_by', '=', 'creator.id')
            ->select('m.id as mitra_id', 'm.name', DB::raw('SUM(t.total) as total'), DB::raw('COUNT(*) as cnt'))
            ->whereYear('t.created_at', $year)
            ->whereIn('t.status', $this->salesStatuses)
            ->when(($ownerScopeId !== null), function ($q) use ($ownerScopeId) {
                // Scope ke owner yang sama, dan dukung data legacy (mitra.owner_id null) via created_by owner/tim
                $q->where(function ($w) use ($ownerScopeId) {
                    $w->where('m.owner_id', $ownerScopeId)
                        ->orWhere(function ($ww) use ($ownerScopeId) {
                            $ww->whereNull('m.owner_id')
                                ->where(function ($www) use ($ownerScopeId) {
                                    $www->where('creator.id', $ownerScopeId)
                                        ->orWhere('creator.owner_id', $ownerScopeId);
                                });
                        });
                });
            })
            ->groupBy('m.id', 'm.name')
            ->orderByDesc(DB::raw('SUM(t.total)'))
            ->limit($limit)
            ->get();
        return response()->json($rows);
    }

    // GET /api/analytics/outlet-products?mitra_id=&year=
    public function outletProducts(Request $request)
    {
        $request->validate(['mitra_id' => ['required', 'integer']]);
        $mitraId = (int) $request->query('mitra_id');
        $year = (int) ($request->query('year') ?? Carbon::now()->year);
        $rows = DB::table('transaction_items as ti')
            ->join('transactions as t', 'ti.transaction_id', '=', 't.id')
            ->join('products as p', 'ti.product_id', '=', 'p.id')
            ->select('ti.product_id', 'p.name', DB::raw('SUM(ti.quantity) as qty'), DB::raw('SUM(ti.total) as revenue'))
            ->whereYear('t.created_at', $year)
            ->whereIn('t.status', $this->salesStatuses)
            ->where('t.mitra_id', $mitraId)
            ->groupBy('ti.product_id', 'p.name')
            ->orderByDesc(DB::raw('SUM(ti.quantity)'))
            ->get();
        return response()->json($rows);
    }
}
