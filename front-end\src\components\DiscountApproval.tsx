import React, { useState } from 'react';
import { useAuth } from '../context/AuthContext';
// import { mockTransactions } from '../services/mockData';
import { apiFetch } from '../services/api';
import { Transaction } from '../types';
import { sendDiscountApprovalNotification } from '../services/whatsappService';
import { Check, X, AlertCircle } from 'lucide-react';

const Toast: React.FC<{ type: 'success' | 'error'; message: string }> = ({ type, message }) => (
  <div className={`fixed top-6 right-6 z-50 px-4 py-3 rounded-lg shadow-lg text-sm ${type === 'success' ? 'bg-green-600 text-white' : 'bg-red-600 text-white'}`}>
    {message}
  </div>
);

const DiscountApproval: React.FC = () => {
  const { user } = useAuth();
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [toast, setToast] = useState<{ type: 'success' | 'error'; message: string } | null>(null);

  React.useEffect(() => {
    (async () => {
      try {
        const res = await apiFetch('/api/transactions?status=pending_owner');
        const items: any[] = res.data ?? res;
        setTransactions(items.filter(t => (t.discount_type ?? 'none') !== 'none').map(t => ({
          id: String(t.id),
          mitraId: String(t.mitra_id ?? ''),
          mitraName: t.mitra_name ?? t.mitra?.name ?? 'Mitra',
          salesId: String(t.sales_id ?? ''),
          salesName: t.sales_name ?? t.sales?.name ?? 'Sales',
          mitraPhone: t.mitra?.phone ?? '',
          items: (t.items ?? []).map((i: any) => ({
            productId: String(i.product_id ?? ''), productName: i.product?.name ?? i.product_name ?? '',
            price: Number(i.price), quantity: Number(i.quantity), discount: Number(i.discount), total: Number(i.total)
          })),
          subtotal: Number(t.subtotal),
          discountType: t.discount_type ?? 'none',
          discountValue: Number(t.discount_value ?? 0),
          discountReason: t.discount_reason ?? '',
          total: Number(t.total),
          paymentMethod: t.payment_method ?? 'cash',
          status: t.status,
          dueDate: t.due_date ?? undefined,
          approvedBy: String(t.approved_by ?? ''),
          rejectionReason: t.rejection_reason ?? '',
          shippingDocument: t.shipping_document ?? '',
          deliveryDocument: t.delivery_document ?? '',
          createdAt: t.created_at ?? new Date().toISOString(),
          updatedAt: t.updated_at ?? new Date().toISOString(),
        })));
      } catch (e) { console.error(e); }
    })();
  }, []);
  const [selectedTransaction, setSelectedTransaction] = useState<Transaction | null>(null);
  const [rejectionReason, setRejectionReason] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('id-ID', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const handleApproveDiscount = async (transactionId: string) => {
    setIsProcessing(true);
    try {
      await apiFetch(`/api/transactions/${transactionId}/approve-owner`, { method: 'POST' });
      setTransactions(prev => prev.filter(t => t.id !== transactionId));
      // Broadcast per flow: owner acc -> mitra, gudang, sales
      const rec = await apiFetch(`/api/transactions/${transactionId}/recipients`);
      if (rec?.mitra) await sendDiscountApprovalNotification(rec.mitra, transactionId, 'approved');
      if (rec?.sales) await sendDiscountApprovalNotification(rec.sales, transactionId, 'approved');
      if (Array.isArray(rec?.admin_gudang)) {
        for (const g of rec.admin_gudang) {
          await sendDiscountApprovalNotification(g, transactionId, 'approved');
        }
      }
      setToast({ type: 'success', message: 'Diskon berhasil disetujui!' });
      setSelectedTransaction(null);
    } catch (error) {
      console.error('Error approving discount:', error);
      setToast({ type: 'error', message: 'Gagal menyetujui diskon' });
      setTimeout(() => setToast(null), 3000);
    } finally {
      setIsProcessing(false);
    }
  };

  const handleRejectDiscount = async (transactionId: string) => {
    if (!rejectionReason.trim()) {
      setToast({ type: 'error', message: 'Alasan penolakan wajib diisi' });
      setTimeout(() => setToast(null), 3000);
      return;
    }

    setIsProcessing(true);
    try {
      await apiFetch(`/api/transactions/${transactionId}/reject`, {
        method: 'POST',
        body: JSON.stringify({ rejection_reason: rejectionReason })
      });
      setTransactions(prev => prev.filter(t => t.id !== transactionId));
      // Broadcast per flow: owner reject -> mitra dan sales (sertakan alasan)
      const rec = await apiFetch(`/api/transactions/${transactionId}/recipients`);
      if (rec?.mitra) await sendDiscountApprovalNotification(rec.mitra, transactionId, 'rejected', rejectionReason);
      if (rec?.sales) await sendDiscountApprovalNotification(rec.sales, transactionId, 'rejected', rejectionReason);
      setToast({ type: 'success', message: 'Diskon berhasil ditolak!' });
      setSelectedTransaction(null);
      setRejectionReason('');
    } catch (error) {
      console.error('Error rejecting discount:', error);
      setToast({ type: 'error', message: 'Gagal menolak diskon' });
    } finally {
      setIsProcessing(false);
      if (toast) setTimeout(() => setToast(null), 3000);
    }
  };

  return (
    <div className="space-y-6">
      {toast && <Toast type={toast.type} message={toast.message} />}

      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900">Approval Pengajuan Diskon</h1>
        <div className="flex items-center space-x-2 text-orange-600">
          <AlertCircle size={20} />
          <span className="text-sm font-medium">{transactions.length} pengajuan menunggu</span>
        </div>
      </div>

      {transactions.length === 0 ? (
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-8 text-center">
          <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <Check size={32} className="text-green-600" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">Tidak Ada Pengajuan Diskon</h3>
          <p className="text-gray-600">Semua pengajuan diskon telah diproses.</p>
        </div>
      ) : (
        <div className="grid gap-6">
          {transactions.map((transaction) => (
            <div key={transaction.id} className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
              <div className="flex items-start justify-between mb-4">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">{transaction.id}</h3>
                  <p className="text-sm text-gray-600">{transaction.mitraName} - {transaction.salesName}</p>
                  <p className="text-xs text-gray-500">{formatDate(transaction.createdAt)}</p>
                </div>
                <span className="px-3 py-1 bg-orange-100 text-orange-700 text-sm font-semibold rounded-full">
                  Pending Approval
                </span>
              </div>

              {/* Transaction Summary */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div className="space-y-3">
                  <div>
                    <span className="text-sm text-gray-600">Subtotal:</span>
                    <span className="ml-2 font-medium">{formatCurrency(transaction.subtotal)}</span>
                  </div>
                  <div>
                    <span className="text-sm text-gray-600">Tipe Diskon:</span>
                    <span className="ml-2 font-medium capitalize">
                      {transaction.discountType === 'total' ? 'Total Keseluruhan' : 'Per Item'}
                    </span>
                  </div>
                  <div>
                    <span className="text-sm text-gray-600">Nilai Diskon:</span>
                    <span className="ml-2 font-medium text-red-600">{
                      (() => {
                        if (transaction.discountType === 'per_item') {
                          const sumItemDisc = (transaction.items || []).reduce((acc, it) => acc + (Number(it.discount) || 0), 0);
                          return formatCurrency(sumItemDisc);
                        }
                        return formatCurrency(transaction.discountValue);
                      })()
                    }</span>
                  </div>
                  <div>
                    <span className="text-sm text-gray-600">Total Setelah Diskon:</span>
                    <span className="ml-2 font-bold text-lg">{
                      (() => {
                        const subtotal = transaction.subtotal || 0;
                        const disc = transaction.discountType === 'per_item'
                          ? (transaction.items || []).reduce((acc, it) => acc + (Number(it.discount) || 0), 0)
                          : (transaction.discountValue || 0);
                        return formatCurrency(Math.max(0, subtotal - disc));
                      })()
                    }</span>
                  </div>
                </div>

                <div className="space-y-3">
                  <div>
                    <span className="text-sm text-gray-600">Metode Pembayaran:</span>
                    <span className="ml-2 font-medium capitalize">{transaction.paymentMethod}</span>
                  </div>
                  {transaction.dueDate && (
                    <div>
                      <span className="text-sm text-gray-600">Jatuh Tempo:</span>
                      <span className="ml-2 font-medium">{formatDate(transaction.dueDate)}</span>
                    </div>
                  )}
                  <div>
                    <span className="text-sm text-gray-600">Jumlah Item:</span>
                    <span className="ml-2 font-medium">{transaction.items.length} item</span>
                  </div>
                </div>
              </div>

              {/* Discount Reason */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">Alasan Pengajuan Diskon:</label>
                <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                  <p className="text-sm text-yellow-800">{transaction.discountReason}</p>
                </div>
              </div>

              {/* Items Preview */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-3">Items Pesanan:</label>
                <div className="border border-gray-200 rounded-lg overflow-hidden">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500">Produk</th>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500">Harga</th>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500">Qty</th>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500">Diskon Item</th>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500">Total</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200">
                      {transaction.items.map((item, index) => (
                        <tr key={index} className="bg-white">
                          <td className="px-4 py-2 text-sm text-gray-900">{item.productName}</td>
                          <td className="px-4 py-2 text-sm text-gray-900">{formatCurrency(item.price)}</td>
                          <td className="px-4 py-2 text-sm text-gray-900">{item.quantity}</td>
                          <td className="px-4 py-2 text-sm text-red-600">{
                            transaction.discountType === 'per_item'
                              ? formatCurrency(item.discount)
                              : formatCurrency(Math.round(((item.price * item.quantity) / (transaction.subtotal || 1)) * transaction.discountValue))
                          }</td>
                          <td className="px-4 py-2 text-sm font-medium text-gray-900">{
                            (() => {
                              const base = item.price * item.quantity;
                              const disc = transaction.discountType === 'per_item'
                                ? item.discount
                                : Math.round(((item.price * item.quantity) / (transaction.subtotal || 1)) * transaction.discountValue);
                              return formatCurrency(base - disc);
                            })()
                          }</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex flex-col sm:flex-row gap-4">
                <div className="flex-1">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Alasan Penolakan (Jika ditolak) <span className="text-red-500">*</span>
                  </label>
                  <textarea
                    value={selectedTransaction?.id === transaction.id ? rejectionReason : ''}
                    onChange={(e) => {
                      setRejectionReason(e.target.value);
                      setSelectedTransaction(transaction);
                    }}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500"
                    rows={2}
                    placeholder="Berikan alasan jika diskon ditolak..."
                  />
                </div>
                <div className="flex flex-col gap-2 sm:w-48">
                  <button
                    onClick={() => handleApproveDiscount(transaction.id)}
                    disabled={isProcessing}
                    className="flex items-center justify-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 transition-colors"
                  >
                    <Check size={16} className="mr-2" />
                    {isProcessing ? 'Memproses...' : 'Setujui Diskon'}
                  </button>
                  <button
                    onClick={() => handleRejectDiscount(transaction.id)}
                    disabled={isProcessing || (selectedTransaction?.id === transaction.id && !rejectionReason.trim())}
                    className="flex items-center justify-center px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50 transition-colors"
                  >
                    <X size={16} className="mr-2" />
                    {isProcessing ? 'Memproses...' : 'Tolak Diskon'}
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default DiscountApproval;