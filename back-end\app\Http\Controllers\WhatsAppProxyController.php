<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;

class WhatsAppProxyController extends Controller
{
    /**
     * Proxy WhatsApp API requests to avoid CORS from the browser.
     * Accepts JSON: { receiptnumber, message, imageurl?, documenturl? }
     */
    public function send(Request $request)
    {
        $data = $request->validate([
            'receiptnumber' => ['required', 'string'],
            'message' => ['required', 'string'],
            'imageurl' => ['nullable', 'string'],
            'documenturl' => ['nullable', 'string'],
        ]);

        $baseUrl = config('services.whatsapp.base_url', env('WHATSAPP_API_URL', 'https://wablasingateway.com/api'));
        $deviceToken = config('services.whatsapp.device_token', env('WHATSAPP_DEVICE_TOKEN', 'R0RiQ0s4YVc3cTJtUmJHZXdVc2tGTTZoaFhycCtnK25DbUV2S2lubEVoYz0='));
        $apiKey = config('services.whatsapp.api_key', env('WHATSAPP_API_KEY', '5edd0789e6774467990c1d939503b444'));

        try {
            // Build multipart form
            $multipart = [
                [ 'name' => 'token', 'contents' => $deviceToken ],
                [ 'name' => 'inquiry', 'contents' => 'send-messages' ],
                [ 'name' => 'receiptnumber', 'contents' => $data['receiptnumber'] ],
                [ 'name' => 'message', 'contents' => $data['message'] ],
                [ 'name' => 'apikey', 'contents' => $apiKey ],
            ];
            if (!empty($data['imageurl'])) {
                $multipart[] = [ 'name' => 'imageurl', 'contents' => $data['imageurl'] ];
            }
            if (!empty($data['documenturl'])) {
                $multipart[] = [ 'name' => 'documenturl', 'contents' => $data['documenturl'] ];
            }

            $response = Http::asMultipart()->post($baseUrl, $multipart);

            if (!$response->successful()) {
                return response()->json([
                    'RESULT' => 'ERROR',
                    'status' => $response->status(),
                    'body' => $response->json() ?? $response->body(),
                ], 502);
            }

            return response()->json($response->json());
        } catch (\Throwable $e) {
            return response()->json([
                'RESULT' => 'ERROR',
                'message' => $e->getMessage(),
            ], 500);
        }
    }
}

