<?php

namespace App\Http\Controllers;

use App\Models\StockLog;
use Illuminate\Http\Request;

class StockLogController extends Controller
{
    public function index(Request $request)
    {
        $user = $request->user();
        $query = StockLog::with('product')
            ->join('products', 'stock_logs.product_id', '=', 'products.id')
            ->join('users as creator', 'products.created_by', '=', 'creator.id')
            ->select('stock_logs.*');

        if ($user && $user->role !== 'superadmin') {
            $ownerScopeId = $user->role === 'owner' ? $user->id : $user->owner_id;
            if ($ownerScopeId) {
                $query->where(function ($q) use ($ownerScopeId) {
                    $q->where('creator.id', $ownerScopeId)
                        ->orWhere('creator.owner_id', $ownerScopeId);
                });
            } else {
                $query->where('products.created_by', $user->id);
            }
        }

        if ($request->has('product_id')) {
            $query->where('stock_logs.product_id', $request->query('product_id'));
        }
        return $query->orderBy('products.name', 'asc')->paginate(50);
    }
}
