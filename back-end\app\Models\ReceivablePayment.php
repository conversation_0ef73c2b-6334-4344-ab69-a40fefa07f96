<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ReceivablePayment extends Model
{
    use HasFactory;

    protected $fillable = [
        'mitra_id', 'transaction_id', 'amount', 'paid_at', 'note', 'created_by'
    ];

    protected $casts = [
        'paid_at' => 'datetime',
    ];

    public function mitra()
    {
        return $this->belongsTo(Mitra::class);
    }

    public function transaction()
    {
        return $this->belongsTo(Transaction::class);
    }
}

