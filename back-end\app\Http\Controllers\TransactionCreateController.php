<?php

namespace App\Http\Controllers;

use App\Models\Transaction;
use App\Models\TransactionItem;
use App\Models\Product;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Carbon;

class TransactionCreateController extends Controller
{
    public function store(Request $request)
    {
        $data = $request->validate([
            'mitra_id' => ['required', 'exists:mitras,id'],
            'sales_id' => ['required', 'exists:users,id'],
            'items' => ['required', 'array', 'min:1'],
            'items.*.product_id' => ['required', 'exists:products,id'],
            'items.*.price' => ['required', 'numeric', 'min:0'],
            'items.*.quantity' => ['required', 'integer', 'min:1'],
            'items.*.discount' => ['nullable', 'numeric', 'min:0'],
            // client item.total/subtotal/total can be inconsistent; we'll recompute server-side
            'subtotal' => ['required', 'numeric', 'min:0'],
            'discount_type' => ['required', 'in:none,total,per_item'],
            'discount_value' => ['required', 'numeric', 'min:0'],
            'discount_reason' => ['nullable', 'string'],
            'total' => ['required', 'numeric', 'min:0'],
            'payment_method' => ['required', 'in:cash,credit'],
            'status' => ['required', 'in:pending_owner,pending_gudang'],
            'due_date' => ['nullable', 'date'],
        ]);

        // Normalize and validate totals to prevent invalid discount submissions
        $perItemMode = $data['discount_type'] === 'per_item';
        $normalizedItems = [];
        $computedSubtotal = 0;
        foreach ($data['items'] as $it) {
            $price = (int) $it['price'];
            $qty = (int) $it['quantity'];
            $base = $price * $qty;
            // In per_item mode, discount value is per 1 quantity. Total discount = perItemDiscount * qty
            $perItemDiscount = (int)($it['discount'] ?? 0);
            $disc = $perItemMode ? max(0, min($perItemDiscount * $qty, $base)) : 0;
            $itemTotal = max(0, $base - $disc);
            // Store the per-item discount (unit) for client visibility, but persist computed total as well
            $normalizedItems[] = array_merge($it, ['discount' => $perItemDiscount, 'total' => $itemTotal]);
            $computedSubtotal += $itemTotal;
        }

        $discountTotal = $perItemMode ? 0 : (int) $data['discount_value'];
        if (!$perItemMode && $discountTotal >= $computedSubtotal) {
            return response()->json(['message' => 'Nilai diskon total harus lebih kecil dari subtotal'], 422);
        }
        $computedTotal = max(0, $computedSubtotal - $discountTotal);

        // Overwrite client-sent amounts with server computed values
        $data['items'] = $normalizedItems;
        $data['subtotal'] = $computedSubtotal;
        $data['total'] = $computedTotal;

        // New credit policy:
        // - First outstanding credit for a mitra goes straight to gudang (auto owner approval)
        // - Second and subsequent outstanding credits require owner approval
        // Also: if there is a discount, always require owner approval
        $requiresOwnerApproval = false;
        if ($data['payment_method'] === 'credit') {
            $ongoingCreditCount = Transaction::where('mitra_id', $data['mitra_id'])
                ->where('payment_method', 'credit')
                ->whereIn('status', ['pending_owner', 'pending_gudang', 'approved', 'shipped', 'delivered'])
                ->count();
            if ($ongoingCreditCount >= 1) {
                $requiresOwnerApproval = true; // this is the second (or more) credit
            }
        }
        // Determine if discount is applied
        $perItemDiscTotal = 0;
        foreach ($normalizedItems as $ni) {
            $perItemDiscTotal += ((int)($ni['discount'] ?? 0)) * ((int)($ni['quantity'] ?? 0));
        }
        $hasDiscount = $data['discount_type'] === 'per_item'
            ? ($perItemDiscTotal > 0)
            : ((int)$data['discount_value'] > 0);
        if ($hasDiscount) {
            $requiresOwnerApproval = true;
        }

        $finalStatus = $requiresOwnerApproval ? 'pending_owner' : 'pending_gudang';

        return DB::transaction(function () use ($data, $finalStatus) {
            // Auto set approved_by (owner) only when we bypass owner approval due to first credit
            $approvedBy = null;
            if ($data['payment_method'] === 'credit' && $finalStatus === 'pending_gudang') {
                $sales = \App\Models\User::find($data['sales_id']);
                if ($sales && $sales->owner_id) {
                    $approvedBy = $sales->owner_id;
                }
            }

            $transaction = Transaction::create([
                'mitra_id' => $data['mitra_id'],
                'sales_id' => $data['sales_id'],
                'subtotal' => $data['subtotal'],
                'discount_type' => $data['discount_type'],
                'discount_value' => $data['discount_value'],
                'discount_reason' => $data['discount_reason'] ?? null,
                'total' => $data['total'],
                'payment_method' => $data['payment_method'],
                'status' => $finalStatus,
                'approved_by' => $approvedBy,
                'due_date' => isset($data['due_date']) ? Carbon::parse($data['due_date'])->format('Y-m-d H:i:s') : null,
                'created_by' => request()->user()->id ?? null,
            ]);

            // Preload product names to avoid N+1 queries
            $productMap = Product::whereIn('id', array_column($data['items'], 'product_id'))->get()->keyBy('id');

            foreach ($data['items'] as $item) {
                $product = $productMap[$item['product_id']] ?? null;
                TransactionItem::create([
                    'transaction_id' => $transaction->id,
                    'product_id' => $item['product_id'],
                    'product_name' => $product?->name ?? '',
                    'price' => $item['price'],
                    'quantity' => $item['quantity'],
                    'discount' => $item['discount'] ?? 0,
                    'total' => $item['total'],
                ]);
                // Optionally reduce stock at approval stage, not now
            }

            return response()->json($transaction->load('items'), 201);
        });
    }
}
