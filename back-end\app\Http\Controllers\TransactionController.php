<?php

namespace App\Http\Controllers;

use App\Models\Transaction;
use App\Models\StockLog;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;

class TransactionController extends Controller
{
    public function index(Request $request)
    {
        $query = Transaction::with(['items', 'items.product', 'mitra:id,name,phone', 'sales:id,name,phone']);

        // Basic filters
        if ($status = $request->query('status')) {
            $query->where('status', $status);
        }
        if ($salesId = $request->query('sales_id')) {
            $query->where('sales_id', $salesId);
        }
        if ($mitraId = $request->query('mitra_id')) {
            $query->where('mitra_id', $mitraId);
        }

        // Scope by owner for non-superadmin (owner, admin_gudang, sales)
        if ($request->user() && $request->user()->role !== 'superadmin') {
            $user = $request->user();
            $ownerScopeId = $user->role === 'owner' ? $user->id : $user->owner_id;

            // Sales: enforce only their own transactions
            if ($user->role === 'sales') {
                $query->where('sales_id', $user->id);
            }

            if ($ownerScopeId) {
                $query->where(function ($qq) use ($ownerScopeId) {
                    $qq->whereHas('sales', function ($q) use ($ownerScopeId) {
                        $q->where('owner_id', $ownerScopeId)->orWhere('id', $ownerScopeId);
                    })->orWhereHas('mitra', function ($q) use ($ownerScopeId) {
                        $q->where('owner_id', $ownerScopeId);
                    });
                });
            }
        }

        $perPage = (int) $request->query('per_page', 20);
        return $query->latest()->paginate($perPage);
    }

    public function approveOwner(Transaction $transaction)
    {
        $transaction->update([
            'status' => 'pending_gudang',
            'approved_by' => request()->user()->id ?? null,
        ]);

        // Kirim notifikasi ke admin gudang milik owner yang sama
        try {
            $recipients = app(\App\Http\Controllers\TransactionController::class)->recipients($transaction)->getData(true);
            $phones = $recipients['admin_gudang'] ?? [];
            if (!empty($phones)) {
                foreach ($phones as $phone) {
                    try {
                        Http::post(url('/api/whatsapp/send'), [
                            'receiptnumber' => $phone,
                            'message' => sprintf(
                                "Ada transaksi baru menunggu persetujuan gudang\nNo: TXN-%d\nMitra: %s\nTotal: %s",
                                $transaction->id,
                                optional($transaction->mitra)->name ?? '-',
                                number_format((int)$transaction->total, 0, ',', '.')
                            )
                        ]);
                    } catch (\Throwable $e) { /* ignore individual phone failure */
                    }
                }
            }
        } catch (\Throwable $e) { /* ignore notification failure */
        }

        return response()->json($transaction);
    }

    public function reject(Transaction $transaction)
    {
        $data = request()->validate([
            'rejection_reason' => ['required', 'string']
        ]);
        $transaction->update([
            'status' => 'rejected',
            'rejection_reason' => $data['rejection_reason'],
        ]);
        return response()->json($transaction);
    }

    public function approveGudang(Transaction $transaction)
    {
        return DB::transaction(function () use ($transaction) {
            // Kurangi stok untuk setiap item
            foreach ($transaction->items as $item) {
                if ($item->product_id) {
                    $product = $item->product; // via relation
                    if ($product) {
                        $prev = $product->stock;
                        $new = max(0, $prev - (int) $item->quantity);
                        $product->update(['stock' => $new]);

                        StockLog::create([
                            'product_id' => $product->id,
                            'type' => 'out',
                            'quantity' => (int) $item->quantity,
                            'previous_stock' => $prev,
                            'new_stock' => $new,
                            'reason' => 'Transaksi disetujui gudang',
                            'created_by' => request()->user()->id ?? null,
                        ]);
                    }
                }
            }

            // Jika pembayaran hutang, tambahkan hutang mitra setelah transaksi disetujui gudang
            if ($transaction->payment_method === 'credit') {
                $mitra = $transaction->mitra()->lockForUpdate()->first();
                if ($mitra) {
                    $mitra->update([
                        'current_debt' => max(0, (int)$mitra->current_debt + (int)$transaction->total)
                    ]);
                }
            }

            // Update status transaksi
            $transaction->update([
                'status' => 'approved',
                'approved_by' => request()->user()->id ?? null,
            ]);

            return response()->json($transaction->fresh());
        });
    }

    public function ship(Transaction $transaction)
    {
        // Accept either simple string or uploaded file (image/document)
        if (request()->hasFile('shipping_document')) {
            $file = request()->file('shipping_document');
            $path = $file->store('shipping', 'public');
            $url = asset('storage/' . $path);
            $transaction->update([
                'status' => 'shipped',
                'shipping_document' => $url,
            ]);
        } else {
            $data = request()->validate([
                'shipping_document' => ['required', 'string']
            ]);
            $transaction->update([
                'status' => 'shipped',
                'shipping_document' => $data['shipping_document'],
            ]);
        }
        return response()->json($transaction);
    }

    public function deliver(Transaction $transaction)
    {
        if (request()->hasFile('delivery_document')) {
            $file = request()->file('delivery_document');
            $path = $file->store('delivery', 'public');
            $url = asset('storage/' . $path);
            $transaction->update([
                'status' => 'delivered',
                'delivery_document' => $url,
            ]);
        } else {
            $data = request()->validate([
                'delivery_document' => ['required', 'string']
            ]);
            $transaction->update([
                'status' => 'delivered',
                'delivery_document' => $data['delivery_document'],
            ]);
        }
        return response()->json($transaction);
    }

    public function recipients(Transaction $transaction)
    {
        $transaction->load(['mitra:id,phone,owner_id', 'sales:id,phone,owner_id']);
        $mitraPhone = optional($transaction->mitra)->phone;
        $salesPhone = optional($transaction->sales)->phone;

        // Resolve owner: prefer sales.owner_id, fallback to mitra.owner_id
        $ownerId = null;
        if ($transaction->sales && $transaction->sales->owner_id) {
            $ownerId = $transaction->sales->owner_id;
        } elseif ($transaction->mitra && $transaction->mitra->owner_id) {
            $ownerId = $transaction->mitra->owner_id;
        }

        $ownerPhone = $ownerId ? \App\Models\User::where('id', $ownerId)->value('phone') : null;
        // Final fallback: if owner is still not resolved (e.g., fresh data without owner links), pick the first owner
        if (!$ownerPhone) {
            $fallbackOwner = \App\Models\User::where('role', 'owner')->first();
            $ownerPhone = $fallbackOwner?->phone;
            $ownerId = $ownerId ?: $fallbackOwner?->id;
        }

        $adminGudang = $ownerId ? \App\Models\User::where('role', 'admin_gudang')
            ->where('owner_id', $ownerId)
            ->pluck('phone')
            ->filter()
            ->values()
            ->all() : [];

        return response()->json([
            'mitra' => $mitraPhone,
            'sales' => $salesPhone,
            'owner' => $ownerPhone,
            'admin_gudang' => $adminGudang,
        ]);
    }
}
