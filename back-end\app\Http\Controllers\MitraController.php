<?php

namespace App\Http\Controllers;

use App\Models\Mitra;
use Illuminate\Http\Request;

class MitraController extends Controller
{
    public function index(Request $request)
    {
        $perPage = (int) $request->query('per_page', 20);
        $query = Mitra::query();
        $user = $request->user();
        if ($user && $user->role !== 'superadmin') {
            $ownerScopeId = $user->role === 'owner' ? $user->id : $user->owner_id;
            if ($ownerScopeId) {
                // Tampilkan mitra milik owner scope, dan fallback untuk data lama yang owner_id masih null tapi dibuat oleh owner/teammate
                $query->leftJoin('users as creator', 'mitras.created_by', '=', 'creator.id')
                    ->where(function ($q) use ($ownerScopeId) {
                        $q->where('mitras.owner_id', $ownerScopeId)
                            ->orWhere(function ($w) use ($ownerScopeId) {
                                $w->whereNull('mitras.owner_id')
                                    ->where(function ($ww) use ($ownerScopeId) {
                                        $ww->where('creator.id', $ownerScopeId)
                                            ->orWhere('creator.owner_id', $ownerScopeId);
                                    });
                            });
                    })
                    ->select('mitras.*');
            } else {
                // Fallback ketat: hanya data yang dibuat oleh user
                $query->where('created_by', $user->id);
            }
        }
        return $query->latest()->paginate($perPage);
    }

    public function store(Request $request)
    {
        $data = $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'phone' => ['required', 'string', 'max:50'],
            'address' => ['required', 'string'],
            'credit_limit' => ['nullable', 'integer', 'min:0'],
            'current_debt' => ['nullable', 'integer', 'min:0'],
            'status' => ['required', 'in:active,inactive']
        ]);
        $user = $request->user();
        $data['created_by'] = $user->id ?? null;
        if ($user) {
            if ($user->role === 'owner') $data['owner_id'] = $user->id;
            elseif (in_array($user->role, ['admin_gudang', 'sales'])) $data['owner_id'] = $user->owner_id;
        }
        $mitra = Mitra::create($data);
        return response()->json($mitra, 201);
    }

    public function update(Request $request, Mitra $mitra)
    {
        $user = $request->user();
        if ($user && $user->role !== 'superadmin') {
            $ownerScopeId = $user->role === 'owner' ? $user->id : $user->owner_id;
            if (!$ownerScopeId || $mitra->owner_id !== $ownerScopeId) {
                return response()->json(['message' => 'Forbidden'], 403);
            }
        }

        $data = $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'phone' => ['required', 'string', 'max:50'],
            'address' => ['required', 'string'],
            'credit_limit' => ['nullable', 'integer', 'min:0'],
            'current_debt' => ['nullable', 'integer', 'min:0'],
            'status' => ['required', 'in:active,inactive']
        ]);
        $mitra->update($data);
        return response()->json($mitra);
    }

    public function destroy(Request $request, Mitra $mitra)
    {
        $user = $request->user();
        if ($user && $user->role !== 'superadmin') {
            $ownerScopeId = $user->role === 'owner' ? $user->id : $user->owner_id;
            if (!$ownerScopeId || $mitra->owner_id !== $ownerScopeId) {
                return response()->json(['message' => 'Forbidden'], 403);
            }
        }

        $mitra->delete();
        return response()->json(['ok' => true]);
    }
}
