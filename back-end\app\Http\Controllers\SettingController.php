<?php

namespace App\Http\Controllers;

use App\Models\Setting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class SettingController extends Controller
{
    public function show(Request $request)
    {
        $user = $request->user();
        if (!$user || $user->role !== 'owner') return response()->json(['message' => 'Forbidden'], 403);
        $setting = Setting::firstOrCreate(['owner_id' => $user->id], [
            'business_name' => $user->name . "'s Business",
            'address' => '',
            'logo_url' => null,
        ]);
        return response()->json($setting);
    }

    public function update(Request $request)
    {
        $user = $request->user();
        if (!$user || $user->role !== 'owner') return response()->json(['message' => 'Forbidden'], 403);

        // Accept either JSON (logo_url string) or multipart with 'logo' file
        $data = $request->validate([
            'business_name' => ['required', 'string', 'max:255'],
            'address' => ['nullable', 'string'],
            'logo_url' => ['nullable', 'string'],
            'logo' => ['nullable', 'image', 'max:2048'], // up to ~2MB
        ]);

        $setting = Setting::firstOrCreate(['owner_id' => $user->id]);

        // If a file is uploaded, store it and update logo_url
        if ($request->hasFile('logo')) {
            $path = $request->file('logo')->store('logos', 'public');
            $url = asset('storage/' . $path);
            $data['logo_url'] = $url;
        }

        $setting->update($data);
        return response()->json($setting);
    }

    public function uploadLogo(Request $request)
    {
        $user = $request->user();
        if (!$user || $user->role !== 'owner') return response()->json(['message' => 'Forbidden'], 403);
        $request->validate([
            'logo' => ['required', 'image', 'max:2048'],
        ]);
        $path = $request->file('logo')->store('logos', 'public');
        $url = asset('storage/' . $path);
        return response()->json(['url' => $url]);
    }
}
