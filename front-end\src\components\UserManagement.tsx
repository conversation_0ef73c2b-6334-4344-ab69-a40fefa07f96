import React, { useState } from 'react';
import { apiFetch } from '../services/api';
import { User } from '../types';
import { Plus, Edit, Trash2, Search, Users, Shield, User as UserIcon, UserCheck, X } from 'lucide-react';

// Simple Toast
const Toast: React.FC<{ type: 'success' | 'error'; message: string }> = ({ type, message }) => (
  <div className={`fixed top-6 right-6 z-50 px-4 py-3 rounded-lg shadow-lg text-sm ${type === 'success' ? 'bg-green-600 text-white' : 'bg-red-600 text-white'}`}>
    {message}
  </div>
);

const UserManagement: React.FC = () => {
  const [users, setUsers] = useState<User[]>([]);
  const [toast, setToast] = useState<{ type: 'success' | 'error'; message: string } | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  React.useEffect(() => {
    (async () => {
      try {
        const res = await apiFetch('/api/users');
        const items: any[] = res.data ?? res;
        const mapped = items.map((u: any) => ({
          id: String(u.id), name: u.name, email: u.email, phone: u.phone ?? '', role: u.role,
          ownerId: u.owner_id != null ? String(u.owner_id) : null,
          createdAt: u.created_at ?? new Date().toISOString()
        }));
        setUsers(mapped);
        const ownerItems = items.filter((u: any) => u.role === 'owner');
        setOwners(ownerItems.map((u: any) => ({ id: String(u.id), name: u.name, email: u.email, phone: u.phone ?? '', role: 'owner', createdAt: u.created_at ?? new Date().toISOString() })));
      } catch (e) { console.error(e); }
    })();
  }, []);

  const [roleFilter, setRoleFilter] = useState<'all' | User['role']>('all');
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingUser, setEditingUser] = useState<User | null>(null);
  const [owners, setOwners] = useState<User[]>([]);

  // Keep owners list in sync with users so newly created owners appear immediately
  React.useEffect(() => {
    setOwners(users.filter((u) => u.role === 'owner'));
  }, [users]);

  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    role: 'sales' as User['role'],
    ownerId: '' as string,
    password: ''
  });

  // Load owners for Super Admin to assign
  React.useEffect(() => {
    (async () => {
      try {
        const me = await apiFetch('/api/auth/me');
        if (me?.role === 'superadmin') {
          const res = await apiFetch('/api/users');
          const items: any[] = res.data ?? res;
          const ownersList = items.filter((u: any) => u.role === 'owner');
          setOwners(ownersList.map((u: any) => ({ id: String(u.id), name: u.name, email: u.email, phone: u.phone ?? '', role: 'owner', createdAt: u.created_at ?? new Date().toISOString() })));
        }
      } catch (e) { console.error(e); }
    })();
  }, []);

  const getRoleLabel = (role: User['role']) => {
    const labels: Record<User['role'], string> = {
      'superadmin': 'Super Admin',
      'owner': 'Owner',
      'admin_gudang': 'Admin Gudang',
      'sales': 'Sales'
    };
    return labels[role];
  };

  const getRoleIcon = (role: User['role']) => {
    const icons: Record<User['role'], React.ElementType> = {
      'superadmin': Shield,
      'owner': UserCheck,
      'admin_gudang': Users,
      'sales': UserIcon
    };
    return icons[role];
  };

  const getRoleColor = (role: User['role']) => {
    const colors: Record<User['role'], string> = {
      'superadmin': 'bg-purple-100 text-purple-700',
      'owner': 'bg-blue-100 text-blue-700',
      'admin_gudang': 'bg-green-100 text-green-700',
      'sales': 'bg-orange-100 text-orange-700'
    };
    return colors[role];
  };

  const filteredUsers = users.filter(user => {
    const matchesSearch = user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.phone.includes(searchTerm);
    const matchesRole = roleFilter === 'all' || user.role === roleFilter;
    return matchesSearch && matchesRole;
  });

  const openModal = (user?: User) => {
    if (user) {
      setEditingUser(user);
      setFormData({
        name: user.name,
        email: user.email,
        phone: user.phone,
        role: user.role,
        ownerId: (user as any).ownerId ?? ((user as any).owner_id != null ? String((user as any).owner_id) : ''),
        password: ''
      });
    } else {
      setEditingUser(null);
      setFormData({
        name: '',
        email: '',
        phone: '',
        role: 'sales',
        ownerId: '',
        password: ''
      });
    }
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
    setEditingUser(null);
    setFormData({
      name: '',
      email: '',
      phone: '',
      role: 'sales',
      ownerId: '',
      password: ''
    });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.name.trim() || !formData.email.trim() || !formData.phone.trim()) {
      setToast({ type: 'error', message: 'Semua field wajib diisi' });
      setTimeout(() => setToast(null), 2500);
      return;
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(formData.email)) {
      setToast({ type: 'error', message: 'Format email tidak valid' });
      setTimeout(() => setToast(null), 2500);
      return;
    }

    if (formData.phone.length < 10) {
      setToast({ type: 'error', message: 'Nomor telepon minimal 10 digit' });
      setTimeout(() => setToast(null), 2500);
      return;
    }

    if (!editingUser) {
      const pw = formData.password;
      if (!pw || !pw.trim()) { setToast({ type: 'error', message: 'Password wajib diisi dan tidak boleh hanya spasi' }); setTimeout(() => setToast(null), 2500); return; }
      if (pw.trim().length < 6) { setToast({ type: 'error', message: 'Password minimal 6 karakter (abaikan spasi di depan/belakang)' }); setTimeout(() => setToast(null), 2500); return; }
    }

    (async () => {
      try {
        if (editingUser) {
          const payload: any = { name: formData.name, email: formData.email, phone: formData.phone, role: formData.role };
          if ((formData.role === 'sales' || formData.role === 'admin_gudang')) {
            payload.owner_id = formData.ownerId ? formData.ownerId : null;
          }
          if (formData.password && formData.password.trim()) payload.password = formData.password.trim();
          const updated = await apiFetch(`/api/users/${editingUser.id}`, { method: 'PUT', body: JSON.stringify(payload) });
          setUsers(prev => prev.map(u => u.id === editingUser.id ? ({ id: String(updated.id), name: updated.name, email: updated.email, phone: updated.phone ?? '', role: updated.role, createdAt: updated.created_at ?? new Date().toISOString() }) : u));
          setToast({ type: 'success', message: 'User berhasil diupdate!' });
          setTimeout(() => setToast(null), 2500);
        } else {
          if (!formData.password) { setToast({ type: 'error', message: 'Password wajib diisi' }); setTimeout(() => setToast(null), 2500); return; }
          const payload: any = { name: formData.name, email: formData.email, phone: formData.phone, role: formData.role, password: formData.password.trim() };
          if ((formData.role === 'sales' || formData.role === 'admin_gudang') && formData.ownerId) payload.owner_id = formData.ownerId;
          const created = await apiFetch('/api/users', { method: 'POST', body: JSON.stringify(payload) });
          setUsers(prev => [...prev, { id: String(created.id), name: created.name, email: created.email, phone: created.phone ?? '', role: created.role, createdAt: created.created_at ?? new Date().toISOString() }]);
          setToast({ type: 'success', message: 'User baru berhasil ditambahkan!' });
          setTimeout(() => setToast(null), 2500);
        }
        closeModal();
      } catch (err: any) {
        setToast({ type: 'error', message: err.message || 'Gagal menyimpan user' });
        setTimeout(() => setToast(null), 2500);
      }
    })();
  };

  const handleDelete = (userId: string) => {
    if (!window.confirm('Apakah Anda yakin ingin menghapus user ini?')) return;
    (async () => {
      try {
        await apiFetch(`/api/users/${userId}`, { method: 'DELETE' });
        setUsers(prev => prev.filter(user => user.id !== userId));
        setToast({ type: 'success', message: 'User berhasil dihapus!' });
        setTimeout(() => setToast(null), 2500);
      } catch (e: any) {
        setToast({ type: 'error', message: e.message || 'Gagal menghapus user' });
        setTimeout(() => setToast(null), 2500);
      }
    })();
  };

  return (
    <div className="space-y-6" >
      {toast && <Toast type={toast.type} message={toast.message} />}
      < div className="flex items-center justify-between" >
        <h1 className="text-3xl font-bold text-gray-900">Management User</h1>
        <button
          onClick={() => openModal()}
          className="flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
        >
          <Plus size={20} className="mr-2" />
          Tambah User
        </button>
      </div >

      {/* Filters */}
      < div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100" >
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="relative">
            <Search size={20} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500"
              placeholder="Cari user..."
            />
          </div>
          <select
            value={roleFilter}
            onChange={(e) => setRoleFilter(e.target.value as 'all' | User['role'])}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500"
          >
            <option value="all">Semua Role</option>
            <option value="superadmin">Super Admin</option>
            <option value="owner">Owner</option>
            <option value="admin_gudang">Admin Gudang</option>
            <option value="sales">Sales</option>
          </select>
          <div className="text-sm text-gray-600 flex items-center">
            Total: {filteredUsers.length} user
          </div>
        </div>
      </div >

      {/* Users Grid */}
      < div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6" >
        {
          filteredUsers.map((user) => {
            const RoleIcon = getRoleIcon(user.role);

            return (
              <div key={user.id} className="bg-white rounded-xl shadow-sm border border-gray-100 p-6 hover:shadow-md transition-shadow">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center mb-3">
                      <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mr-3">
                        <RoleIcon size={20} className="text-green-600" />
                      </div>
                      <div className="min-w-0">
                        <h3 className="text-lg font-semibold text-gray-900 truncate" title={user.name}>{user.name}</h3>
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getRoleColor(user.role)}`}>
                          {getRoleLabel(user.role)}
                        </span>
                      </div>
                    </div>

                    <div className="space-y-2 text-sm text-gray-600">
                      <div className="flex items-center min-w-0 overflow-hidden">
                        <span className="font-medium mr-2 flex-shrink-0">Email:</span>
                        <span className="truncate" title={user.email}>{user.email}</span>
                      </div>
                      <div className="flex items-center min-w-0 overflow-hidden">
                        <span className="font-medium mr-2 flex-shrink-0">Telepon:</span>
                        <span className="truncate" title={user.phone}>{user.phone}</span>
                      </div>
                      <div className="flex items-center min-w-0">
                        <span className="font-medium mr-2 flex-shrink-0">Dibuat:</span>
                        <span className="truncate">{new Date(user.createdAt).toLocaleDateString('id-ID')}</span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex items-center justify-end space-x-2 pt-4 border-t border-gray-100">
                  <button
                    onClick={() => openModal(user)}
                    className="p-2 text-blue-600 hover:bg-blue-50 rounded"
                    title="Edit User"
                  >
                    <Edit size={16} />
                  </button>
                  <button
                    onClick={() => handleDelete(user.id)}
                    className="p-2 text-red-600 hover:bg-red-50 rounded"
                    title="Hapus User"
                  >
                    <Trash2 size={16} />
                  </button>
                </div>
              </div>
            );
          })
        }
      </div >

      {
        filteredUsers.length === 0 && (
          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-12 text-center">
            <Users size={48} className="mx-auto text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Tidak Ada User</h3>
            <p className="text-gray-600 mb-4">
              {searchTerm || roleFilter !== 'all'
                ? 'Tidak ada user yang sesuai dengan filter'
                : 'Belum ada user ditambahkan'}
            </p>
            {!searchTerm && roleFilter === 'all' && (
              <button
                onClick={() => openModal()}
                className="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
              >
                <Plus size={16} className="mr-2" />
                Tambah User Pertama
              </button>
            )}
          </div>
        )
      }

      {/* User Modal */}
      {
        isModalOpen && (
          <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div className="relative top-20 mx-auto p-5 border w-11/12 max-w-md shadow-lg rounded-lg bg-white">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-bold text-gray-900">
                  {editingUser ? 'Edit User' : 'Tambah User Baru'}
                </h3>
                <button
                  onClick={closeModal}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <X size={24} />
                </button>
              </div>

              <form onSubmit={handleSubmit} className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Nama Lengkap *
                  </label>
                  <input
                    type="text"
                    value={formData.name}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500"
                    placeholder="Masukkan nama lengkap"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Email *
                  </label>
                  <input
                    type="email"
                    value={formData.email}
                    onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500"
                    placeholder="<EMAIL>"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Nomor Telepon *
                  </label>
                  <input
                    type="tel"
                    inputMode="numeric"
                    pattern="[0-9]*"
                    value={formData.phone}
                    onChange={(e) => {
                      const digitsOnly = e.target.value.replace(/[^0-9]/g, '');
                      setFormData({ ...formData, phone: digitsOnly });
                    }}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500"
                    placeholder="628xxxxxxxxxx"
                    required
                  />

                  {(formData.role === 'sales' || formData.role === 'admin_gudang') && (
                    <div className="mt-4">
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Owner
                      </label>
                      <select
                        value={formData.ownerId}
                        onChange={(e) => setFormData({ ...formData, ownerId: e.target.value })}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500"
                      >
                        <option value="">Pilih Owner</option>
                        {owners.map((o) => (
                          <option key={o.id} value={o.id}>{o.name}</option>
                        ))}
                      </select>
                      <p className="text-xs text-gray-500 mt-1">Opsional bagi Super Admin. Owner akan otomatis terisi jika dibuat oleh Owner.</p>
                    </div>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Role *
                  </label>
                  <select
                    value={formData.role}
                    onChange={(e) => setFormData({ ...formData, role: e.target.value as User['role'] })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500"
                    required
                  >
                    <option value="sales">Sales</option>
                    <option value="admin_gudang">Admin Gudang</option>
                    <option value="owner">Owner</option>
                    <option value="superadmin">Super Admin</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Password {editingUser && (<span className="text-xs text-gray-500">(opsional saat edit)</span>)}
                  </label>
                  <input
                    type="password"
                    value={formData.password}
                    onChange={(e) => setFormData({ ...formData, password: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500"
                    placeholder={editingUser ? 'Kosongkan jika tidak ingin mengubah' : 'Minimal 6 karakter'}
                    minLength={6}
                    required={!editingUser}
                  />
                </div>


                <div className="flex justify-end space-x-4">
                  <button
                    type="button"
                    onClick={closeModal}
                    className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
                  >
                    Batal
                  </button>
                  <button
                    type="submit"
                    className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
                  >
                    {editingUser ? 'Update User' : 'Tambah User'}
                  </button>
                </div>
              </form>
            </div>
          </div>
        )
      }
    </div >
  );
};

export default UserManagement;