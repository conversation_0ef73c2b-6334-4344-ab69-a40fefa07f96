import React, { useEffect, useMemo, useState } from 'react';
import { apiFetch } from '../services/api';
import { useAuth } from '../context/AuthContext';
import { Plus, Filter, Download } from 'lucide-react';

const formatCurrency = (amount: number) => new Intl.NumberFormat('id-ID', { style: 'currency', currency: 'IDR', minimumFractionDigits: 0 }).format(amount || 0);
const formatDate = (iso?: string) => {
  if (!iso) return '-';
  const d = new Date(iso);
  return isNaN(d.getTime()) ? '-' : d.toLocaleDateString('id-ID');
};


const StatCard: React.FC<{ title: string; value: React.ReactNode }> = ({ title, value }) => (
  <div className="bg-white rounded-xl border shadow-sm p-4">
    <div className="text-xs font-medium text-gray-500 uppercase tracking-wide">{title}</div>
    <div className="mt-1 text-lg md:text-xl font-semibold text-gray-900">{value}</div>
  </div>
);


const Receivables: React.FC = () => {
  const { user } = useAuth();
  const [payments, setPayments] = useState<any[]>([]);
  const [mitras, setMitras] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [form, setForm] = useState({ mitra_id: '', amount: 0, paid_at: '', note: '' });
  const [filterMitra, setFilterMitra] = useState('');

  const canCreate = user?.role === 'sales' || user?.role === 'owner';
  const isOwner = user?.role === 'owner';

  const filtered = useMemo(() => payments.filter(p => filterMitra ? String(p.mitra_id) === filterMitra : true), [payments, filterMitra]);

  useEffect(() => {
    (async () => {
      setLoading(true);
      try {
        const [mitraRes, payRes] = await Promise.all([
          apiFetch('/api/mitras?per_page=1000'),
          apiFetch('/api/receivable-payments?per_page=100'),
        ]);
        const mitraItems = Array.isArray(mitraRes?.data) ? mitraRes.data : (mitraRes?.data?.data || mitraRes || []);
        const payItems = Array.isArray(payRes?.data) ? payRes.data : (payRes?.data?.data || payRes || []);
        setMitras(mitraItems);
        setPayments(payItems);
      } catch (e) { console.error(e); }
      finally { setLoading(false); }
    })();
  }, []);

  const submit = async () => {
    try {
      const payload = {
        mitra_id: Number(form.mitra_id),
        amount: Number(form.amount),
        paid_at: form.paid_at ? new Date(form.paid_at).toISOString().slice(0, 19).replace('T', ' ') : undefined,
        note: form.note || undefined,
      };
      const created = await apiFetch('/api/receivable-payments', { method: 'POST', body: JSON.stringify(payload) });
      setPayments(prev => [created, ...prev]);
      setIsModalOpen(false);
      setForm({ mitra_id: '', amount: 0, paid_at: '', note: '' });
      // Refresh mitras to reflect updated current_debt
      const mitraRes = await apiFetch('/api/mitras?per_page=1000');
      const mitraItems = Array.isArray(mitraRes?.data) ? mitraRes.data : (mitraRes?.data?.data || mitraRes || []);
      setMitras(mitraItems);
    } catch (e) { console.error(e); alert('Gagal mencatat pembayaran'); }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900">Pencatatan Piutang</h1>
        <div className="flex items-center space-x-2">
          {canCreate && (
            <button onClick={() => setIsModalOpen(true)} className="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700">
              <Plus size={18} className="mr-2" /> Tambah Pembayaran
            </button>
          )}
          <button className="px-3 py-2 border rounded-lg text-gray-700 bg-white hover:bg-gray-50"><Download size={18} className="inline mr-2" />Export CSV</button>
        </div>
      </div>

      {/* Summary for owner */}
      {isOwner && (
        <div className="grid grid-cols-1 lg:grid-cols-12 gap-4">
          <div className="lg:col-span-3 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-1 gap-4">
            <StatCard title="Total Pembayaran" value={formatCurrency(filtered.reduce((s, p) => s + Number(p.amount || 0), 0))} />
            <StatCard title="Mitra Membayar" value={new Set(filtered.map(p => p.mitra_id)).size} />
            <StatCard title="Total Record" value={filtered.length} />
          </div>

          <div className="lg:col-span-9">
            <div className="bg-white p-5 rounded-xl shadow-sm border">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-base md:text-lg font-semibold">Mitra yang Masih Punya Hutang</h3>
              </div>
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200 text-sm">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Mitra</th>
                      <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Hutang</th>
                      <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Limit</th>
                      <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {mitras.filter((m: any) => Number(m.current_debt || 0) > 0).map((m: any) => (
                      <tr key={m.id}>
                        <td className="px-4 py-2 whitespace-nowrap">{m.name}</td>
                        <td className="px-4 py-2 whitespace-nowrap">{formatCurrency(Number(m.current_debt || 0))}</td>
                        <td className="px-4 py-2 whitespace-nowrap">{formatCurrency(Number(m.credit_limit || 0))}</td>
                        <td className="px-4 py-2 whitespace-nowrap">
                          <span className={`inline-flex px-2 py-0.5 text-[11px] font-semibold rounded-full ${Number(m.current_debt || 0) > Number(m.credit_limit || 0) ? 'bg-red-100 text-red-700' : 'bg-yellow-100 text-yellow-700'}`}>
                            {Number(m.current_debt || 0) > Number(m.credit_limit || 0) ? 'Melebihi Limit' : 'Berjalan'}
                          </span>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      )}

      <div className="bg-white p-6 rounded-xl shadow-sm border">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold">Daftar Pembayaran</h3>
          <div className="flex items-center space-x-2">
            <Filter size={18} className="text-gray-500" />
            <select value={filterMitra} onChange={e => setFilterMitra(e.target.value)} className="border rounded px-3 py-2 text-sm">
              <option value="">Semua Mitra</option>
              {mitras.map((m: any) => <option value={m.id} key={m.id}>{m.name}</option>)}
            </select>
          </div>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tanggal</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Mitra</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Nominal</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Catatan</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filtered.map((p: any) => (
                <tr key={p.id}>
                  <td className="px-6 py-4 whitespace-nowrap text-sm">{(p.paid_at || p.created_at || '').slice(0, 10)}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm">{p.mitra?.name || mitras.find((m: any) => String(m.id) === String(p.mitra_id))?.name || '-'}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm">{formatCurrency(Number(p.amount || 0))}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm">{p.note || '-'}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Modal create */}
      {isModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl shadow-lg p-6 w-full max-w-md">
            <div className="flex items-center justify-between mb-4">
              <h4 className="text-lg font-semibold">Tambah Pembayaran Piutang</h4>
              <button className="text-gray-500" onClick={() => setIsModalOpen(false)}>Tutup</button>
            </div>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">Mitra</label>
                <select className="mt-1 block w-full border rounded px-3 py-2" value={form.mitra_id} onChange={e => setForm({ ...form, mitra_id: e.target.value })}>
                  <option value="">Pilih Mitra</option>
                  {mitras.map((m: any) => <option value={m.id} key={m.id}>{m.name}</option>)}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Tanggal Bayar</label>
                <input type="date" className="mt-1 block w-full border rounded px-3 py-2" value={form.paid_at} onChange={e => setForm({ ...form, paid_at: e.target.value })} />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Nominal</label>
                <input type="number" className="mt-1 block w-full border rounded px-3 py-2" value={form.amount} onChange={e => setForm({ ...form, amount: Number(e.target.value) })} />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Catatan</label>
                <input type="text" className="mt-1 block w-full border rounded px-3 py-2" value={form.note} onChange={e => setForm({ ...form, note: e.target.value })} />
              </div>
            </div>
            <div className="mt-6 flex justify-end space-x-2">
              <button className="px-4 py-2 rounded border" onClick={() => setIsModalOpen(false)}>Batal</button>
              <button className="px-4 py-2 rounded bg-green-600 text-white" onClick={submit} disabled={!form.mitra_id || !form.amount}>Simpan</button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Receivables;

