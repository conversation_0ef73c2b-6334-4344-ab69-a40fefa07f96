import React, { useEffect, useState } from 'react';
import { apiFetch } from '../services/api';

const Settings: React.FC = () => {
  const [businessName, setBusinessName] = useState('');
  const [address, setAddress] = useState('');
  const [logoUrl, setLogoUrl] = useState(''); // current logo URL from server
  const [logoFile, setLogoFile] = useState<File | null>(null); // new file to upload
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [message, setMessage] = useState<string | null>(null);

  useEffect(() => {
    (async () => {
      try {
        const res = await apiFetch('/api/settings');
        setBusinessName(res.business_name || '');
        setAddress(res.address || '');
        setLogoUrl(res.logo_url || '');
      } catch (e) {
        console.error(e);
      } finally {
        setLoading(false);
      }
    })();
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSaving(true);
    try {
      if (logoFile) {
        // Step 1: upload logo file to get URL
        const fd = new FormData();
        fd.append('logo', logoFile);
        const uploadRes = await apiFetch('/api/settings/logo', { method: 'POST', body: fd });
        const newUrl = uploadRes.url || uploadRes.logo_url || logoUrl || '';
        // Step 2: save settings with returned URL
        await apiFetch('/api/settings', {
          method: 'PUT',
          body: JSON.stringify({ business_name: businessName, address, logo_url: newUrl })
        });
      } else {
        await apiFetch('/api/settings', {
          method: 'PUT',
          body: JSON.stringify({ business_name: businessName, address, logo_url: logoUrl || null })
        });
      }
      setMessage('Pengaturan berhasil disimpan');
      setTimeout(() => setMessage(null), 3000);
      // Refresh current settings to reflect new URL if file uploaded
      const latest = await apiFetch('/api/settings');
      setLogoUrl(latest.logo_url || '');
      setLogoFile(null);
    } catch (e) {
      setMessage('Gagal menyimpan pengaturan');
      setTimeout(() => setMessage(null), 3000);
    } finally {
      setSaving(false);
    }
  };

  if (loading) return <div className="p-6">Loading...</div>;

  return (
    <div className="max-w-2xl mx-auto p-6">
      <h2 className="text-2xl font-bold mb-6">Pengaturan Usaha</h2>

      {message && (
        <div className="mb-4 px-4 py-3 rounded bg-green-100 text-green-800">{message}</div>
      )}

      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Nama Usaha</label>
          <input
            type="text"
            className="w-full px-3 py-2 border rounded"
            value={businessName}
            onChange={(e) => setBusinessName(e.target.value)}
            required
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Alamat</label>
          <textarea
            className="w-full px-3 py-2 border rounded"
            rows={3}
            value={address}
            onChange={(e) => setAddress(e.target.value)}
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Logo</label>
          {logoUrl && (
            <div className="mb-2">
              <img src={logoUrl} alt="Logo" className="h-12 object-contain" />
            </div>
          )}
          <input
            type="file"
            accept="image/*"
            className="w-full px-3 py-2 border rounded"
            onChange={(e) => setLogoFile(e.target.files?.[0] || null)}
          />
          <p className="text-xs text-gray-500 mt-1">Anda dapat mengunggah file gambar (png/jpg) atau biarkan kosong untuk mempertahankan logo saat ini.</p>
        </div>
        <div className="pt-2">
          <button type="submit" disabled={saving} className="px-6 py-3 bg-green-600 text-white rounded-lg">
            {saving ? 'Menyimpan...' : 'Simpan'}
          </button>
        </div>
      </form>
    </div>
  );
};

export default Settings;

