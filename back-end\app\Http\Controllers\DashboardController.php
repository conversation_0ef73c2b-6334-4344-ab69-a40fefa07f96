<?php

namespace App\Http\Controllers;

use App\Models\Product;
use App\Models\Transaction;
use App\Models\TransactionItem;
use App\Models\StockLog;
use App\Models\User;
use App\Models\Mitra;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;

class DashboardController extends Controller
{
    public function metrics(Request $request)
    {
        $user = $request->user();
        $role = $user?->role ?? 'guest';
        $salesIdFilter = $role === 'sales' ? $user->id : null;


        // Determine owner scope for non-superadmin
        $ownerScopeId = null;
        if ($user) {
            if ($role === 'owner') $ownerScopeId = $user->id;
            elseif (in_array($role, ['admin_gudang', 'sales'])) $ownerScopeId = $user->owner_id;
        }


        $startOfToday = Carbon::now()->startOfDay();
        $endOfToday = Carbon::now()->endOfDay();
        $startOfWeek = Carbon::now()->startOfWeek();
        $endOfWeek = Carbon::now()->endOfWeek();
        $startOfMonth = Carbon::now()->startOfMonth();
        $endOfMonth = Carbon::now()->endOfMonth();
        $startOfYesterday = Carbon::yesterday()->startOfDay();
        $endOfYesterday = Carbon::yesterday()->endOfDay();
        $startOfLastMonth = Carbon::now()->subMonthNoOverflow()->startOfMonth();
        $endOfLastMonth = Carbon::now()->subMonthNoOverflow()->endOfMonth();

        $lowStockThreshold = (int) ($request->query('low_stock_threshold', 10));

        // Base transaction query; scope by role/owner
        $txBase = Transaction::query();
        if ($role === 'sales') {
            $txBase->where('sales_id', $user->id);
        }
        if ($user && $role !== 'superadmin') {
            // Only transactions under the same owner scope
            $txBase->where(function ($q) use ($ownerScopeId) {
                $q->whereHas('sales', function ($qq) use ($ownerScopeId) {
                    $qq->where('owner_id', $ownerScopeId)->orWhere('id', $ownerScopeId);
                })->orWhereHas('mitra', function ($qq) use ($ownerScopeId) {
                    $qq->where('owner_id', $ownerScopeId);
                });
            });
        }

        // Totals - scope by owner for non-superadmin
        if ($user && $role !== 'superadmin') {
            $totalUsers = User::where(function ($q) use ($ownerScopeId) {
                $q->where('id', $ownerScopeId)
                    ->orWhere('owner_id', $ownerScopeId);
            })->count();

            // Mitra: owner scope + legacy (owner_id null tapi dibuat oleh owner/tim)
            $totalMitras = Mitra::leftJoin('users as creator', 'mitras.created_by', '=', 'creator.id')
                ->where(function ($q) use ($ownerScopeId) {
                    $q->where('mitras.owner_id', $ownerScopeId)
                        ->orWhere(function ($w) use ($ownerScopeId) {
                            $w->whereNull('mitras.owner_id')
                                ->where(function ($ww) use ($ownerScopeId) {
                                    $ww->where('creator.id', $ownerScopeId)
                                        ->orWhere('creator.owner_id', $ownerScopeId);
                                });
                        });
                })->count('mitras.id');

            $totalProducts = Product::join('users as creator', 'products.created_by', '=', 'creator.id')
                ->where(function ($q) use ($ownerScopeId) {
                    $q->where('creator.id', $ownerScopeId)
                        ->orWhere('creator.owner_id', $ownerScopeId);
                })->count('products.id');
        } else {
            $totalUsers = User::count();
            $totalMitras = Mitra::count();
            $totalProducts = Product::count();
        }
        $totalTransactions = (clone $txBase)->count();

        $pendingOwner = (clone $txBase)->where('status', 'pending_owner')->count();
        $pendingGudang = (clone $txBase)->where('status', 'pending_gudang')->count();

        $countsByStatus = [
            'pending_owner' => (clone $txBase)->where('status', 'pending_owner')->count(),
            'pending_gudang' => (clone $txBase)->where('status', 'pending_gudang')->count(),
            'approved' => (clone $txBase)->where('status', 'approved')->count(),
            'rejected' => (clone $txBase)->where('status', 'rejected')->count(),
            'shipped' => (clone $txBase)->where('status', 'shipped')->count(),
            'delivered' => (clone $txBase)->where('status', 'delivered')->count(),
        ];

        // Shipments today (approx by updated_at with current status)
        $shippedToday = (clone $txBase)
            ->where('status', 'shipped')
            ->whereBetween('updated_at', [$startOfToday, $endOfToday])
            ->count();
        $deliveredToday = (clone $txBase)
            ->where('status', 'delivered')
            ->whereBetween('updated_at', [$startOfToday, $endOfToday])
            ->count();

        // Sales totals (sum of total) for approved/shipped/delivered
        $salesStatuses = ['approved', 'shipped', 'delivered'];
        $salesToday = (clone $txBase)
            ->whereIn('status', $salesStatuses)
            ->whereBetween('created_at', [$startOfToday, $endOfToday])
            ->sum('total');
        $salesWeek = (clone $txBase)
            ->whereIn('status', $salesStatuses)
            ->whereBetween('created_at', [$startOfWeek, $endOfWeek])
            ->sum('total');
        $salesMonth = (clone $txBase)
            ->whereIn('status', $salesStatuses)
            ->whereBetween('created_at', [$startOfMonth, $endOfMonth])
            ->sum('total');

        // Overall revenues cards
        $revenueTotal = (clone $txBase)->whereIn('status', $salesStatuses)->sum('total');
        $revenueCash = (clone $txBase)->whereIn('status', $salesStatuses)->where('payment_method', 'cash')->sum('total');
        if ($user && $role !== 'superadmin') {
            $receivableOutstanding = (int) DB::table('mitras')->where('owner_id', $ownerScopeId)->sum('current_debt');
        } else {
            $receivableOutstanding = (int) DB::table('mitras')->sum('current_debt');
        }

        // Transactions counts per period
        $transactionsToday = (clone $txBase)
            ->whereBetween('created_at', [$startOfToday, $endOfToday])
            ->count();
        $transactionsWeek = (clone $txBase)
            ->whereBetween('created_at', [$startOfWeek, $endOfWeek])
            ->count();
        // Baselines for trends
        $salesYesterday = (clone $txBase)
            ->whereIn('status', $salesStatuses)
            ->whereBetween('created_at', [$startOfYesterday, $endOfYesterday])
            ->sum('total');
        $approvedThisMonth = (clone $txBase)
            ->where('status', 'approved')
            ->whereBetween('created_at', [$startOfMonth, $endOfMonth])
            ->count();
        $approvedLastMonth = (clone $txBase)
            ->where('status', 'approved')
            ->whereBetween('created_at', [$startOfLastMonth, $endOfLastMonth])
            ->count();

        $transactionsMonth = (clone $txBase)
            ->whereBetween('created_at', [$startOfMonth, $endOfMonth])
            ->count();
        $transactionsLastMonth = (clone $txBase)
            ->whereBetween('created_at', [$startOfLastMonth, $endOfLastMonth])
            ->count();

        // Low stock scoped by owner
        if ($user && $role !== 'superadmin') {
            $lowStockCount = Product::join('users as creator', 'products.created_by', '=', 'creator.id')
                ->where('products.stock', '<=', $lowStockThreshold)
                ->where(function ($q) use ($ownerScopeId) {
                    $q->where('creator.id', $ownerScopeId)
                        ->orWhere('creator.owner_id', $ownerScopeId);
                })->count('products.id');
        } else {
            $lowStockCount = Product::where('stock', '<=', $lowStockThreshold)->count();
        }

        // Activities & Analytics (scoped)
        $stockActivitiesQuery = StockLog::with('product')
            ->join('products', 'stock_logs.product_id', '=', 'products.id')
            ->join('users as creator', 'products.created_by', '=', 'creator.id')
            ->select('stock_logs.*')
            ->latest();
        if ($user && $role !== 'superadmin') {
            $stockActivitiesQuery->where(function ($q) use ($ownerScopeId) {
                $q->where('creator.id', $ownerScopeId)
                    ->orWhere('creator.owner_id', $ownerScopeId);
            });
        }
        $stockActivities = $stockActivitiesQuery->take(5)->get()->map(function ($l) {
            return [
                'type' => 'stock',
                'message' => sprintf('Stok %s %s qty %d', optional($l->product)->name ?? '-', $l->type, (int) $l->quantity),
                'at' => optional($l->created_at)->toDateTimeString(),
            ];
        })->toArray();

        $txActivitiesQuery = (clone $txBase)->latest();
        $txActivities = $txActivitiesQuery->take(5)->get()->map(function ($t) {
            return [
                'type' => 'transaction',
                'message' => sprintf('Transaksi #%d %s total %d', $t->id, $t->status, (int) $t->total),
                'at' => optional($t->created_at)->toDateTimeString(),
            ];
        })->toArray();
        $activities = array_slice(array_merge($stockActivities, $txActivities), 0, 10);

        $year = Carbon::now()->year;
        $monthlySales = (clone $txBase)
            ->select(DB::raw('MONTH(created_at) as month'), DB::raw('SUM(total) as total'), DB::raw('COUNT(*) as cnt'))
            ->whereYear('created_at', $year)
            ->whereIn('status', $salesStatuses)
            ->groupBy(DB::raw('MONTH(created_at)'))
            ->orderBy(DB::raw('MONTH(created_at)'))
            ->get();

        $topProducts = TransactionItem::query()
            ->select('product_id', DB::raw('SUM(quantity) as qty'), DB::raw('SUM(total) as revenue'))
            ->whereHas('transaction', function ($q) use ($salesIdFilter, $salesStatuses, $user, $role, $ownerScopeId) {
                $q->whereIn('status', $salesStatuses);
                if ($salesIdFilter) {
                    $q->where('sales_id', $salesIdFilter);
                }
                if ($user && $role !== 'superadmin') {
                    $q->where(function ($qq) use ($ownerScopeId) {
                        $qq->whereHas('sales', function ($s) use ($ownerScopeId) {
                            $s->where('owner_id', $ownerScopeId)->orWhere('id', $ownerScopeId);
                        })->orWhereHas('mitra', function ($m) use ($ownerScopeId) {
                            $m->where('owner_id', $ownerScopeId);
                        });
                    });
                }
            })
            ->groupBy('product_id')
            ->orderByDesc(DB::raw('SUM(total)'))
            ->with('product:id,name')
            ->take(5)
            ->get();

        $topMitras = DB::table('transactions')
            ->join('mitras', 'transactions.mitra_id', '=', 'mitras.id')
            ->select('transactions.mitra_id as mitra_id', 'mitras.name as name', DB::raw('SUM(transactions.total) as total'), DB::raw('COUNT(*) as cnt'))
            ->whereIn('transactions.status', $salesStatuses)
            ->when($salesIdFilter, function ($q) use ($salesIdFilter) {
                return $q->where('transactions.sales_id', $salesIdFilter);
            })
            ->when($user && $role !== 'superadmin', function ($q) use ($ownerScopeId) {
                $q->whereExists(function ($sub) use ($ownerScopeId) {
                    $sub->select(DB::raw(1))
                        ->from('users as s')
                        ->whereColumn('s.id', 'transactions.sales_id')
                        ->where(function ($w) use ($ownerScopeId) {
                            $w->where('s.owner_id', $ownerScopeId)->orWhere('s.id', $ownerScopeId);
                        });
                })
                    ->orWhere('mitras.owner_id', $ownerScopeId);
            })
            ->groupBy('transactions.mitra_id', 'mitras.name')
            ->orderByDesc('total')
            ->limit(5)
            ->get();

        $pendingOwnerList = (clone $txBase)
            ->leftJoin('mitras', 'transactions.mitra_id', '=', 'mitras.id')
            ->where('transactions.status', 'pending_owner')
            ->latest('transactions.created_at')
            ->take(5)
            ->get(['transactions.id', 'transactions.mitra_id', 'mitras.name as mitra_name', 'transactions.total', 'transactions.created_at']);

        $pendingGudangList = (clone $txBase)
            ->leftJoin('mitras', 'transactions.mitra_id', '=', 'mitras.id')
            ->where('transactions.status', 'pending_gudang')
            ->latest('transactions.created_at')
            ->take(5)
            ->get(['transactions.id', 'transactions.mitra_id', 'mitras.name as mitra_name', 'transactions.total', 'transactions.created_at']);

        if ($user && $role !== 'superadmin') {
            $lowStockList = Product::join('users as creator', 'products.created_by', '=', 'creator.id')
                ->where('products.stock', '<=', $lowStockThreshold)
                ->where(function ($q) use ($ownerScopeId) {
                    $q->where('creator.id', $ownerScopeId)
                        ->orWhere('creator.owner_id', $ownerScopeId);
                })
                ->orderBy('products.stock')
                ->take(10)
                ->get(['products.id as id', 'products.name as name', 'products.stock as stock']);
        } else {
            $lowStockList = Product::where('stock', '<=', $lowStockThreshold)
                ->orderBy('stock')
                ->take(10)
                ->get(['id', 'name', 'stock']);
        }

        $recentTransactions = (clone $txBase)
            ->leftJoin('mitras', 'transactions.mitra_id', '=', 'mitras.id')
            ->latest('transactions.created_at')
            ->take(5)
            ->get(['transactions.id', 'transactions.mitra_id', 'mitras.name as mitra_name', 'transactions.total', 'transactions.status', 'transactions.created_at']);

        return response()->json([
            'total' => [
                'users' => $totalUsers,
                'mitras' => $totalMitras,
                'products' => $totalProducts,
                'transactions' => $totalTransactions,
            ],
            'pending' => [
                'owner' => $pendingOwner,
                'gudang' => $pendingGudang,
            ],
            'counts_by_status' => $countsByStatus,
            'shipments' => [
                'shipped_today' => $shippedToday,
                'delivered_today' => $deliveredToday,
            ],
            'sales' => [
                'today' => (int) $salesToday,
                'week' => (int) $salesWeek,
                'month' => (int) $salesMonth,
                'yesterday' => (int) $salesYesterday,
                'trends_period' => [
                    'transactions_month_vs_last_month' => ($transactionsLastMonth > 0)
                        ? round((($transactionsMonth - $transactionsLastMonth) / max(1, $transactionsLastMonth)) * 100)
                        : null,
                ],

                'trends' => [
                    'sales_today_vs_yesterday' => ($salesYesterday > 0)
                        ? round((($salesToday - $salesYesterday) / max(1, $salesYesterday)) * 100)
                        : null,
                    'approved_this_month_vs_last_month' => ($approvedLastMonth > 0)
                        ? round((($approvedThisMonth - $approvedLastMonth) / max(1, $approvedLastMonth)) * 100)
                        : null,
                ],

                'revenue_total' => (int) $revenueTotal,
                'revenue_cash' => (int) $revenueCash,
                'receivable_outstanding' => (int) $receivableOutstanding,
            ],
            'period' => [
                'transactions_today' => $transactionsToday,
                'transactions_week' => $transactionsWeek,
                'transactions_month' => $transactionsMonth,
            ],
            'low_stock_count' => $lowStockCount,
            'activities' => $activities,
            'monthly_sales' => $monthlySales,
            'top_products' => $topProducts,
            'top_mitras' => $topMitras,
            'pending_lists' => [
                'owner' => $pendingOwnerList,
                'gudang' => $pendingGudangList,
            ],
            'low_stock_list' => $lowStockList,
            'recent_transactions' => $recentTransactions,
        ]);
    }
}
