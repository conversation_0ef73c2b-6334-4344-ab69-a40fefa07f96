<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Transaction extends Model
{
    use HasFactory;

    protected $fillable = [
        'mitra_id',
        'sales_id',
        'discount_type',
        'discount_value',
        'discount_reason',
        'subtotal',
        'total',
        'payment_method',
        'status',
        'due_date',
        'approved_by',
        'rejection_reason',
        'shipping_document',
        'delivery_document'
    ];

    public function items()
    {
        return $this->hasMany(TransactionItem::class);
    }

    public function mitra()
    {
        return $this->belongsTo(Mitra::class);
    }

    public function sales()
    {
        return $this->belongsTo(User::class, 'sales_id');
    }
}
