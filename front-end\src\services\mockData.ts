import { Mitra, Category, Product, Transaction, StockLog } from '../types';

export const mockMitras: Mitra[] = [
  {
    id: '1',
    name: 'PT Tani Sejahtera',
    phone: '6281234567890',
    address: 'Jl. Pertanian No. 123, Bandung',
    creditLimit: 200000000,
    currentDebt: 50000000,
    status: 'active',
    createdBy: '4',
    createdAt: '2024-01-15T08:00:00Z'
  },
  {
    id: '2',
    name: '<PERSON><PERSON>',
    phone: '6281234567891',
    address: 'Jl. Sawah Raya No. 456, Jakarta',
    creditLimit: 150000000,
    currentDebt: 0,
    status: 'active',
    createdBy: '4',
    createdAt: '2024-01-20T09:30:00Z'
  },
  {
    id: '3',
    name: '<PERSON><PERSON><PERSON>',
    phone: '6281234567892',
    address: 'Jl. <PERSON>perasi No. 789, Surabaya',
    creditLimit: 300000000,
    currentDebt: 100000000,
    status: 'active',
    createdBy: '4',
    createdAt: '2024-02-01T10:15:00Z'
  }
];

export const mockCategories: Category[] = [
  {
    id: '1',
    name: 'Pupuk',
    description: 'Berbagai jenis pupuk untuk tanaman',
    createdBy: '3',
    createdAt: '2024-01-10T08:00:00Z'
  },
  {
    id: '2',
    name: 'Pestisida',
    description: 'Obat-obatan untuk hama dan penyakit tanaman',
    createdBy: '3',
    createdAt: '2024-01-10T08:30:00Z'
  },
  {
    id: '3',
    name: 'Benih',
    description: 'Benih tanaman berkualitas tinggi',
    createdBy: '3',
    createdAt: '2024-01-10T09:00:00Z'
  },
  {
    id: '4',
    name: 'Alat Pertanian',
    description: 'Peralatan dan mesin pertanian',
    createdBy: '3',
    createdAt: '2024-01-10T09:30:00Z'
  }
];

export const mockProducts: Product[] = [
  {
    id: '1',
    name: 'Pupuk NPK 16-16-16',
    categoryId: '1',
    category: 'Pupuk',
    price: 125000,
    stock: 500,
    unit: 'karung',
    description: 'Pupuk NPK dengan kandungan seimbang untuk semua jenis tanaman',
    createdBy: '3',
    createdAt: '2024-01-12T08:00:00Z'
  },
  {
    id: '2',
    name: 'Insektisida Furadan 3GR',
    categoryId: '2',
    category: 'Pestisida',
    price: 45000,
    stock: 200,
    unit: 'kg',
    description: 'Insektisida sistemik untuk mengendalikan hama tanaman padi',
    createdBy: '3',
    createdAt: '2024-01-12T08:30:00Z'
  },
  {
    id: '3',
    name: 'Benih Padi IR-64',
    categoryId: '3',
    category: 'Benih',
    price: 15000,
    stock: 1000,
    unit: 'kg',
    description: 'Benih padi unggul dengan produktivitas tinggi',
    createdBy: '3',
    createdAt: '2024-01-12T09:00:00Z'
  },
  {
    id: '4',
    name: 'Cangkul Besi',
    categoryId: '4',
    category: 'Alat Pertanian',
    price: 85000,
    stock: 50,
    unit: 'pcs',
    description: 'Cangkul dengan mata besi berkualitas tinggi',
    createdBy: '3',
    createdAt: '2024-01-12T09:30:00Z'
  },
  {
    id: '5',
    name: 'Pupuk Urea',
    categoryId: '1',
    category: 'Pupuk',
    price: 95000,
    stock: 800,
    unit: 'karung',
    description: 'Pupuk nitrogen untuk meningkatkan pertumbuhan tanaman',
    createdBy: '3',
    createdAt: '2024-01-12T10:00:00Z'
  }
];

export const mockStockLogs: StockLog[] = [
  {
    id: '1',
    productId: '1',
    productName: 'Pupuk NPK 16-16-16',
    type: 'in',
    quantity: 100,
    previousStock: 400,
    newStock: 500,
    reason: 'Pembelian dari supplier',
    createdBy: '3',
    createdAt: '2024-02-01T08:00:00Z'
  },
  {
    id: '2',
    productId: '2',
    productName: 'Insektisida Furadan 3GR',
    type: 'out',
    quantity: 50,
    previousStock: 250,
    newStock: 200,
    reason: 'Penjualan ke mitra',
    createdBy: '3',
    createdAt: '2024-02-02T10:30:00Z'
  },
  {
    id: '3',
    productId: '3',
    productName: 'Benih Padi IR-64',
    type: 'adjustment',
    quantity: -20,
    previousStock: 1020,
    newStock: 1000,
    reason: 'Penyesuaian stock opname',
    createdBy: '3',
    createdAt: '2024-02-03T14:15:00Z'
  }
];

export const mockTransactions: Transaction[] = [
  {
    id: 'TXN-001',
    mitraId: '1',
    mitraName: 'PT Tani Sejahtera',
    salesId: '4',
    salesName: 'Sales',
    items: [
      {
        productId: '1',
        productName: 'Pupuk NPK 16-16-16',
        price: 125000,
        quantity: 10,
        discount: 0,
        total: 1250000
      }
    ],
    subtotal: 1250000,
    discountType: 'total',
    discountValue: 50000,
    discountReason: 'Pelanggan setia',
    total: 1200000,
    paymentMethod: 'credit',
    status: 'pending_owner',
    dueDate: '2024-05-15T23:59:59Z',
    createdAt: '2024-02-15T10:00:00Z',
    updatedAt: '2024-02-15T10:00:00Z'
  },
  {
    id: 'TXN-002',
    mitraId: '2',
    mitraName: 'CV Makmur Tani',
    salesId: '4',
    salesName: 'Sales',
    items: [
      {
        productId: '2',
        productName: 'Insektisida Furadan 3GR',
        price: 45000,
        quantity: 20,
        discount: 0,
        total: 900000
      },
      {
        productId: '3',
        productName: 'Benih Padi IR-64',
        price: 15000,
        quantity: 50,
        discount: 0,
        total: 750000
      }
    ],
    subtotal: 1650000,
    discountType: 'none',
    discountValue: 0,
    discountReason: '',
    total: 1650000,
    paymentMethod: 'cash',
    status: 'shipped',
    shippingDocument: 'https://example.com/shipping-doc-002.pdf',
    createdAt: '2024-02-10T14:30:00Z',
    updatedAt: '2024-02-12T09:15:00Z'
  },
  {
    id: 'TXN-003',
    mitraId: '3',
    mitraName: 'Koperasi Maju Bersama',
    salesId: '4',
    salesName: 'Sales',
    items: [
      {
        productId: '4',
        productName: 'Cangkul Besi',
        price: 85000,
        quantity: 5,
        discount: 5000,
        total: 400000
      }
    ],
    subtotal: 425000,
    discountType: 'per_item',
    discountValue: 25000,
    discountReason: 'Pembelian dalam jumlah besar',
    total: 400000,
    paymentMethod: 'cash',
    status: 'delivered',
    shippingDocument: 'https://example.com/shipping-doc-003.pdf',
    deliveryDocument: 'https://example.com/delivery-doc-003.pdf',
    approvedBy: '3',
    createdAt: '2024-02-05T11:20:00Z',
    updatedAt: '2024-02-08T16:45:00Z'
  }
];