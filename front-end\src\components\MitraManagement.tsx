import React, { useState } from 'react';
import { useAuth } from '../context/AuthContext';
// import { mockMitras } from '../services/mockData';
import { apiFetch } from '../services/api';
import { Mitra } from '../types';
import { Plus, Edit, Trash2, Search, Users, Phone, MapPin, CreditCard, X } from 'lucide-react';

// Simple Toast component
const Toast: React.FC<{ type: 'success' | 'error'; message: string }> = ({ type, message }) => (
  <div className={`fixed top-6 right-6 z-50 px-4 py-3 rounded-lg shadow-lg text-sm ${type === 'success' ? 'bg-green-600 text-white' : 'bg-red-600 text-white'}`}>
    {message}
  </div>
);

const MitraManagement: React.FC = () => {
  const { user } = useAuth();
  const [mitras, setMitras] = useState<Mitra[]>([]);
  const [toast, setToast] = useState<{ type: 'success' | 'error'; message: string } | null>(null);
  const [confirm, setConfirm] = useState<{ open: boolean; id?: string; message?: string }>({ open: false });
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'active' | 'inactive'>('all');
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingMitra, setEditingMitra] = useState<Mitra | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    phone: '',
    address: '',
    creditLimit: 0,
    currentDebt: 0,
    status: 'active' as 'active' | 'inactive'
  });

  React.useEffect(() => {
    (async () => {
      try {
        const res = await apiFetch('/api/mitras');
        const items: any[] = res.data ?? res;
        setMitras(items.map(m => ({
          id: String(m.id),
          name: m.name,
          phone: m.phone || '',
          address: m.address || '',
          creditLimit: Number(m.credit_limit || 0),
          currentDebt: Number(m.current_debt || 0),
          status: m.status === 'inactive' ? 'inactive' : 'active',
          createdBy: String(m.created_by ?? ''),
          createdAt: m.created_at ?? new Date().toISOString(),
        })));
      } catch (e) { console.error(e); }
    })();
  }, []);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0
    }).format(amount);
  };

  const filteredMitras = mitras.filter(mitra => {
    const matchesSearch = mitra.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      mitra.phone.includes(searchTerm) ||
      mitra.address.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || mitra.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const openModal = (mitra?: Mitra) => {
    if (mitra) {
      setEditingMitra(mitra);
      setFormData({
        name: mitra.name,
        phone: mitra.phone,
        address: mitra.address,
        creditLimit: mitra.creditLimit,
        currentDebt: mitra.currentDebt,
        status: mitra.status
      });
    } else {
      setEditingMitra(null);
      setFormData({
        name: '',
        phone: '',
        address: '',
        creditLimit: 0,
        currentDebt: 0,
        status: 'active'
      });
    }
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
    setEditingMitra(null);
    setFormData({
      name: '',
      phone: '',
      address: '',
      creditLimit: 0,
      currentDebt: 0,
      status: 'active'
    });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    const missing: string[] = [];
    if (!formData.name.trim()) missing.push('Nama');
    if (!formData.phone.trim()) missing.push('Telepon');
    if (!formData.address.trim()) missing.push('Alamat');
    if (missing.length) {
      setToast({ type: 'error', message: `${missing.join(', ')} wajib diisi` });
      setTimeout(() => setToast(null), 2500);
      return;
    }

    if (formData.phone.length < 10) {
      setToast({ type: 'error', message: 'Nomor telepon minimal 10 digit' });
      setTimeout(() => setToast(null), 2500);
      return;
    }

    if (formData.creditLimit < 0 || formData.currentDebt < 0) {
      setToast({ type: 'error', message: 'Limit kredit dan hutang tidak boleh negatif' });
      setTimeout(() => setToast(null), 2500);
      return;
    }

    (async () => {
      try {
        if (editingMitra) {
          const updated = await apiFetch(`/api/mitras/${editingMitra.id}`, {
            method: 'PUT',
            body: JSON.stringify({
              name: formData.name,
              phone: formData.phone,
              address: formData.address,
              credit_limit: formData.creditLimit,
              current_debt: formData.currentDebt,
              status: formData.status,
            })
          });
          setMitras(prev => prev.map(m => m.id === editingMitra.id ? ({
            id: String(updated.id),
            name: updated.name,
            phone: updated.phone || '',
            address: updated.address || '',
            creditLimit: Number(updated.credit_limit || 0),
            currentDebt: Number(updated.current_debt || 0),
            status: updated.status === 'inactive' ? 'inactive' : 'active',
            createdBy: String(updated.created_by ?? ''),
            createdAt: updated.created_at ?? new Date().toISOString(),
          }) : m));
          setToast({ type: 'success', message: 'Data mitra berhasil diupdate!' });
          setTimeout(() => setToast(null), 2500);
        } else {
          const created = await apiFetch('/api/mitras', {
            method: 'POST',
            body: JSON.stringify({
              name: formData.name,
              phone: formData.phone,
              address: formData.address,
              credit_limit: formData.creditLimit,
              current_debt: formData.currentDebt,
              status: formData.status,
            })
          });
          setMitras(prev => [...prev, {
            id: String(created.id),
            name: created.name,
            phone: created.phone || '',
            address: created.address || '',
            creditLimit: Number(created.credit_limit || 0),
            currentDebt: Number(created.current_debt || 0),
            status: created.status === 'inactive' ? 'inactive' : 'active',
            createdBy: String(created.created_by ?? ''),
            createdAt: created.created_at ?? new Date().toISOString(),
          }]);
          setToast({ type: 'success', message: 'Mitra baru berhasil ditambahkan!' });
          setTimeout(() => setToast(null), 2500);
        }
        closeModal();
      } catch (e: any) {
        setToast({ type: 'error', message: e.message || 'Gagal menyimpan mitra' });
        setTimeout(() => setToast(null), 2500);
      }
    })();
  };

  const handleDelete = (mitraId: string) => {
    if (!window.confirm('Apakah Anda yakin ingin menghapus mitra ini?')) return;
    (async () => {
      try {
        await apiFetch(`/api/mitras/${mitraId}`, { method: 'DELETE' });
        setMitras(prev => prev.filter(mitra => mitra.id !== mitraId));
        setToast({ type: 'success', message: 'Mitra berhasil dihapus!' });
        setTimeout(() => setToast(null), 2500);
      } catch (e: any) {
        setToast({ type: 'error', message: e.message || 'Gagal menghapus mitra' });
        setTimeout(() => setToast(null), 2500);
      }
    })();
  };

  const getCreditStatus = (mitra: Mitra) => {
    const available = mitra.creditLimit - mitra.currentDebt;
    const percentage = (available / mitra.creditLimit) * 100;

    if (percentage >= 50) return { color: 'text-green-600 bg-green-100', label: 'Baik' };
    if (percentage >= 20) return { color: 'text-orange-600 bg-orange-100', label: 'Hati-hati' };
    return { color: 'text-red-600 bg-red-100', label: 'Limit Penuh' };
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900">Management Mitra</h1>
        {toast && <Toast type={toast.type} message={toast.message} />}

        <button
          onClick={() => openModal()}
          className="flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
        >
          <Plus size={20} className="mr-2" />
          Tambah Mitra
        </button>
      </div>

      {/* Filters */}
      <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="relative">
            <Search size={20} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500 truncate"
              placeholder="Cari mitra..."
            />
          </div>
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value as 'all' | 'active' | 'inactive')}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500"
          >
            <option value="all">Semua Status</option>
            <option value="active">Aktif</option>
            <option value="inactive">Tidak Aktif</option>
          </select>
          <div className="text-sm text-gray-600 flex items-center">
            Total: {filteredMitras.length} mitra
          </div>
        </div>
      </div>

      {/* Mitras Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredMitras.map((mitra) => {
          const creditStatus = getCreditStatus(mitra);
          const availableCredit = mitra.creditLimit - mitra.currentDebt;

          return (
            <div key={mitra.id} className="bg-white rounded-xl shadow-sm border border-gray-100 p-6 hover:shadow-md transition-shadow">
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1 min-w-0">
                  <div className="flex items-center mb-2">
                    <Users size={20} className="text-green-600 mr-2" />
                    <h3 className="text-lg font-semibold text-gray-900 truncate" title={mitra.name}>{mitra.name}</h3>
                  </div>
                  <div className="space-y-1 text-sm text-gray-600">
                    <div className="flex items-center">
                      <Phone size={16} className="mr-2" />
                      {mitra.phone}
                    </div>
                    <div className="flex items-start">
                      <MapPin size={16} className="mr-2 mt-0.5 flex-shrink-0" />
                      <span className="line-clamp-2">{mitra.address}</span>
                    </div>
                  </div>
                </div>
                <div className="flex items-center space-x-1">
                  <span className={`px-2 py-1 text-xs font-medium rounded-full ${mitra.status === 'active'
                    ? 'bg-green-100 text-green-700'
                    : 'bg-red-100 text-red-700'
                    }`}>
                    {mitra.status === 'active' ? 'Aktif' : 'Tidak Aktif'}
                  </span>
                </div>
              </div>

              {/* Credit Info */}
              <div className="space-y-3 mb-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Limit Kredit:</span>
                  <span className="text-sm font-medium">{formatCurrency(mitra.creditLimit)}</span>
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Hutang Saat Ini:</span>
                  <span className={`text-sm font-medium ${mitra.currentDebt > 0 ? 'text-red-600' : 'text-gray-900'}`}>
                    {formatCurrency(mitra.currentDebt)}
                  </span>
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Kredit Tersedia:</span>
                  <div className="text-right">
                    <span className="text-sm font-medium text-green-600">
                      {formatCurrency(availableCredit)}
                    </span>
                    <div className={`inline-flex px-2 py-0.5 ml-2 text-xs font-medium rounded-full ${creditStatus.color}`}>
                      {creditStatus.label}
                    </div>
                  </div>
                </div>

                {/* Credit Usage Bar */}
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className={`h-2 rounded-full ${availableCredit >= mitra.creditLimit * 0.5
                      ? 'bg-green-500'
                      : availableCredit >= mitra.creditLimit * 0.2
                        ? 'bg-orange-500'
                        : 'bg-red-500'
                      }`}
                    style={{
                      width: `${Math.max(0, Math.min(100, ((mitra.creditLimit - availableCredit) / mitra.creditLimit) * 100))}%`
                    }}
                  ></div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex items-center justify-between pt-4 border-t border-gray-100">
                <div className="text-xs text-gray-500">
                  Dibuat: {new Date(mitra.createdAt).toLocaleDateString('id-ID')}
                </div>
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => openModal(mitra)}
                    className="p-2 text-blue-600 hover:bg-blue-50 rounded"
                  >
                    <Edit size={16} />
                  </button>
                  {user?.role === 'superadmin' && (
                    <button
                      onClick={() => handleDelete(mitra.id)}
                      className="p-2 text-red-600 hover:bg-red-50 rounded"
                    >
                      <Trash2 size={16} />
                    </button>
                  )}
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {filteredMitras.length === 0 && (
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-12 text-center">
          <Users size={48} className="mx-auto text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Tidak Ada Mitra</h3>
          <p className="text-gray-600 mb-4">
            {searchTerm || statusFilter !== 'all'
              ? 'Tidak ada mitra yang sesuai dengan filter'
              : 'Belum ada mitra ditambahkan'}
          </p>
          {!searchTerm && statusFilter === 'all' && (
            <button
              onClick={() => openModal()}
              className="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
            >
              <Plus size={16} className="mr-2" />
              Tambah Mitra Pertama
            </button>
          )}
        </div>
      )}

      {/* Mitra Modal */}
      {isModalOpen && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-11/12 max-w-2xl shadow-lg rounded-lg bg-white">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-bold text-gray-900">
                {editingMitra ? 'Edit Mitra' : 'Tambah Mitra Baru'}
              </h3>
              <button
                onClick={closeModal}
                className="text-gray-400 hover:text-gray-600"
              >
                <X size={24} />
              </button>
            </div>

            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Nama Mitra *
                  </label>
                  <input
                    type="text"
                    value={formData.name}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500"
                    placeholder="Nama perusahaan/individu"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Nomor Telepon *
                  </label>
                  <input
                    type="tel"
                    inputMode="numeric"
                    pattern="[0-9]*"
                    value={formData.phone}
                    onChange={(e) => setFormData({ ...formData, phone: e.target.value.replace(/[^0-9]/g, '') })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500"
                    placeholder="628xxxxxxxxxx"
                    required
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Alamat *
                </label>
                <textarea
                  value={formData.address}
                  onChange={(e) => setFormData({ ...formData, address: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500"
                  rows={3}
                  placeholder="Alamat lengkap mitra"
                  required
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Limit Kredit (Rp)
                  </label>
                  <input
                    type="number"
                    value={formData.creditLimit}
                    onChange={(e) => setFormData({ ...formData, creditLimit: parseInt(e.target.value) || 0 })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500"
                    min="0"
                    placeholder="0"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Hutang Saat Ini (Rp)
                  </label>
                  <input
                    type="number"
                    value={formData.currentDebt}
                    onChange={(e) => setFormData({ ...formData, currentDebt: parseInt(e.target.value) || 0 })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500"
                    min="0"
                    max={formData.creditLimit}
                    placeholder="0"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Status
                </label>
                <select
                  value={formData.status}
                  onChange={(e) => setFormData({ ...formData, status: e.target.value as 'active' | 'inactive' })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500"
                >
                  <option value="active">Aktif</option>
                  <option value="inactive">Tidak Aktif</option>
                </select>
              </div>

              <div className="flex justify-end space-x-4">
                <button
                  type="button"
                  onClick={closeModal}
                  className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
                >
                  Batal
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
                >
                  {editingMitra ? 'Update Mitra' : 'Tambah Mitra'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default MitraManagement;