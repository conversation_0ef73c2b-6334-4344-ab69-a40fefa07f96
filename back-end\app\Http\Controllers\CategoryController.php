<?php

namespace App\Http\Controllers;

use App\Models\Category;
use Illuminate\Http\Request;

class CategoryController extends Controller
{
    public function index(Request $request)
    {
        $user = $request->user();
        $query = Category::query()->orderBy('name', 'asc');

        if ($user && $user->role !== 'superadmin') {
            $ownerScopeId = $user->role === 'owner' ? $user->id : $user->owner_id;
            if ($ownerScopeId) {
                // Hanya tampilkan kategori yang dibuat oleh owner tersebut atau timnya
                $query->join('users as creator', 'categories.created_by', '=', 'creator.id')
                    ->where(function ($q) use ($ownerScopeId) {
                        $q->where('creator.id', $ownerScopeId)
                            ->orWhere('creator.owner_id', $ownerScopeId);
                    })
                    ->select('categories.*');
            } else {
                // Fallback: jika tidak ada owner scope, batasi ke data yang dibuat oleh user itu sendiri
                $query->where('categories.created_by', $user->id);
            }
        }

        return $query->paginate(20);
    }

    public function store(Request $request)
    {
        $data = $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'description' => ['nullable', 'string']
        ]);
        $data['created_by'] = $request->user()->id ?? null;
        $category = Category::create($data);
        return response()->json($category, 201);
    }

    public function update(Request $request, Category $category)
    {
        $user = $request->user();
        if ($user && $user->role !== 'superadmin') {
            $ownerScopeId = $user->role === 'owner' ? $user->id : $user->owner_id;
            $creator = \App\Models\User::find($category->created_by);
            $creatorOwnerId = $creator ? ($creator->role === 'owner' ? $creator->id : $creator->owner_id) : null;
            if (!$ownerScopeId || !$creatorOwnerId || $ownerScopeId !== $creatorOwnerId) {
                return response()->json(['message' => 'Forbidden'], 403);
            }
        }

        $data = $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'description' => ['nullable', 'string']
        ]);
        $category->update($data);
        return response()->json($category);
    }

    public function destroy(Request $request, Category $category)
    {
        $user = $request->user();
        if ($user && $user->role !== 'superadmin') {
            $ownerScopeId = $user->role === 'owner' ? $user->id : $user->owner_id;
            $creator = \App\Models\User::find($category->created_by);
            $creatorOwnerId = $creator ? ($creator->role === 'owner' ? $creator->id : $creator->owner_id) : null;
            if (!$ownerScopeId || !$creatorOwnerId || $ownerScopeId !== $creatorOwnerId) {
                return response()->json(['message' => 'Forbidden'], 403);
            }
        }

        $category->delete();
        return response()->json(['ok' => true]);
    }
}
