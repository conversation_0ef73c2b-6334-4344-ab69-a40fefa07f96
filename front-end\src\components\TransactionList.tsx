import React, { useState, useEffect } from 'react';
import { useAuth } from '../context/AuthContext';
// import { mockTransactions } from '../services/mockData';
import { apiFetch } from '../services/api';
import { Transaction } from '../types';

import { sendTransactionNotification, sendDiscountApprovalNotification } from '../services/whatsappService';
import {
  Eye,
  Check,
  X,
  Upload,
  Download,
  Clock,
  Truck,
  CheckCircle,
  AlertCircle,
  FileText
} from 'lucide-react';

// Simple toast helper (temporary replacement for alert)
const Toast: React.FC<{ type: 'success' | 'error'; message: string }> = ({ type, message }) => (
  <div className={`fixed top-6 right-6 z-50 px-4 py-3 rounded-lg shadow-lg text-sm ${type === 'success' ? 'bg-green-600 text-white' : 'bg-red-600 text-white'}`}>
    {message}
  </div>
);

interface TransactionListProps {
  userRole: string;
  salesId?: string;
}

const TransactionList: React.FC<TransactionListProps> = ({ userRole, salesId }) => {
  const { user } = useAuth();
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [selectedTransaction, setSelectedTransaction] = useState<Transaction | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [rejectionReason, setRejectionReason] = useState('');
  const [shippingDocument, setShippingDocument] = useState<File | string>('');
  const [deliveryDocument, setDeliveryDocument] = useState<File | string>('');
  const [toast, setToast] = useState<{ type: 'success' | 'error'; message: string } | null>(null);
  useEffect(() => {
    (async () => {
      try {
        const params = new URLSearchParams();
        if (userRole === 'owner') params.set('status', 'pending_owner');
        // Admin gudang: jangan filter status di server agar transaksi yang sudah approved/shipped tetap muncul
        if (userRole === 'sales' && salesId) params.set('sales_id', String(salesId));
        const res = await apiFetch(`/api/transactions${params.size ? `?${params}` : ''}`);
        // Robustly extract array from various API shapes (plain array, paginator, wrapped paginator)
        const payload: any = res;
        const items: any[] = Array.isArray(payload)
          ? payload
          : Array.isArray(payload?.data)
            ? payload.data
            : Array.isArray(payload?.data?.data)
              ? payload.data.data
              : Array.isArray(payload?.items)
                ? payload.items
                : [];
        setTransactions((items || []).map((t: any) => ({
          id: String(t.id),
          mitraId: String(t.mitra_id ?? ''),
          mitraName: t.mitra_name ?? t.mitra?.name ?? 'Mitra',
          mitraPhone: t.mitra?.phone ?? '',
          salesId: String(t.sales_id ?? ''),
          salesName: t.sales_name ?? t.sales?.name ?? 'Sales',
          items: (t.items ?? []).map((i: any) => ({
            productId: String(i.product_id ?? ''),
            productName: i.product?.name ?? i.product_name ?? '',
            price: Number(i.price),
            quantity: Number(i.quantity),
            discount: Number(i.discount),
            total: Number(i.total)
          })),
          subtotal: Number(t.subtotal),
          discountType: t.discount_type ?? 'none',
          discountValue: Number(t.discount_value ?? 0),
          discountReason: t.discount_reason ?? '',
          total: Number(t.total),
          paymentMethod: t.payment_method ?? 'cash',
          status: t.status,
          dueDate: t.due_date ?? undefined,
          approvedBy: String(t.approved_by ?? ''),
          rejectionReason: t.rejection_reason ?? '',
          shippingDocument: t.shipping_document ?? '',
          deliveryDocument: t.delivery_document ?? '',
          createdAt: t.created_at ?? new Date().toISOString(),
          updatedAt: t.updated_at ?? new Date().toISOString(),
        })));
      } catch (e) { console.error(e); }
    })();
  }, [userRole, salesId]);

  const [isProcessing, setIsProcessing] = useState(false);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('id-ID', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusColor = (status: string) => {
    const colors: Record<string, string> = {
      'pending_owner': 'bg-orange-100 text-orange-700',
      'pending_gudang': 'bg-blue-100 text-blue-700',
      'approved': 'bg-green-100 text-green-700',
      'rejected': 'bg-red-100 text-red-700',
      'shipped': 'bg-purple-100 text-purple-700',
      'delivered': 'bg-gray-100 text-gray-700'
    };
    return colors[status] || 'bg-gray-100 text-gray-700';
  };

  const getStatusLabel = (status: string) => {
    const labels: Record<string, string> = {
      'pending_owner': 'Menunggu Persetujuan Owner',
      'pending_gudang': 'Menunggu Persetujuan Gudang',
      'approved': 'Disetujui - Siap Kirim',
      'rejected': 'Ditolak',
      'shipped': 'Sedang Dikirim',
      'delivered': 'Sudah Diterima'
    };
    return labels[status] || status;
  };

  const filteredTransactions = transactions.filter(transaction => {
    if (userRole === 'owner') {
      return transaction.status === 'pending_owner' ||
        (transaction.discountType !== 'none' && transaction.discountValue > 0);
    }
    if (userRole === 'admin_gudang') {
      // Tampilkan transaksi yang relevan untuk gudang di semua tahap operasional
      return ['pending_gudang', 'approved', 'shipped', 'delivered'].includes(transaction.status);
    }
    if (userRole === 'sales') {
      return salesId ? String(transaction.salesId) === String(salesId) : true;
    }
    return true;
  });

  const handleApproveDiscount = async (transactionId: string) => {
    setIsProcessing(true);
    try {
      await apiFetch(`/api/transactions/${transactionId}/approve-owner`, { method: 'POST' });
      setTransactions(prev => prev.map(t => t.id === transactionId ? { ...t, status: 'pending_gudang', updatedAt: new Date().toISOString() } : t));
      setToast({ type: 'success', message: 'Diskon berhasil disetujui!' });
      setIsModalOpen(false);
    } catch (error) {
      console.error('Error approving discount:', error);
      setToast({ type: 'error', message: 'Gagal menyetujui diskon' });
    } finally {
      setIsProcessing(false);
      if (toast) setTimeout(() => setToast(null), 3000);
    }
  };

  const handleRejectDiscount = async (transactionId: string) => {
    if (!rejectionReason.trim()) {
      setToast({ type: 'error', message: 'Alasan penolakan wajib diisi' });
      return;
    }

    setIsProcessing(true);
    try {
      await apiFetch(`/api/transactions/${transactionId}/reject`, {
        method: 'POST',
        body: JSON.stringify({ rejection_reason: rejectionReason })
      });
      setTransactions(prev => prev.map(t => t.id === transactionId ? { ...t, status: 'rejected', rejectionReason, updatedAt: new Date().toISOString() } : t));
      setToast({ type: 'success', message: 'Diskon berhasil ditolak!' });
      setIsModalOpen(false);
      setRejectionReason('');
    } catch (error) {
      console.error('Error rejecting discount:', error);
      setToast({ type: 'error', message: 'Gagal menolak diskon' });
    } finally {
      setIsProcessing(false);
      if (toast) setTimeout(() => setToast(null), 3000);
    }
  };

  const handleApproveTransaction = async (transactionId: string) => {
    setIsProcessing(true);
    try {
      // Update status di backend terlebih dahulu
      const updated = await apiFetch(`/api/transactions/${transactionId}/approve-gudang`, { method: 'POST' });

      // Sinkronkan state frontend berdasarkan respons backend
      setTransactions(prev => prev.map(t => t.id === transactionId ? {
        ...t,
        status: (updated.status as any) ?? 'approved',
        approvedBy: String(updated.approved_by ?? user?.id ?? ''),
        updatedAt: updated.updated_at ?? new Date().toISOString()
      } : t));

      // Opsional: kirim notifikasi WA setelah berhasil update status
      try {
        const tx = transactions.find(t => t.id === transactionId);
        // Kirim notifikasi WA sesuai flow: gudang approve -> mitra & sales
        if (tx) {
          await sendTransactionNotification(tx.mitraPhone || '6280000000000', transactionId, 'approved', tx.mitraName);
          // Ambil phone sales dari backend recipients helper jika perlu
          const recipients = await apiFetch(`/api/transactions/${transactionId}/recipients`);
          if (recipients?.sales) {
            await sendTransactionNotification(recipients.sales, transactionId, 'approved', tx.mitraName);
          }
        }
      } catch (e) {
        console.warn('Gagal mengirim notifikasi WA, melewati...', e);
      }

      setToast({ type: 'success', message: 'Transaksi berhasil disetujui!' });
      setIsModalOpen(false);
    } catch (error) {
      console.error('Error approving transaction:', error);
      setToast({ type: 'error', message: 'Gagal menyetujui transaksi' });
    } finally {
      setIsProcessing(false);
      // Auto hide toast
      if (toast) setTimeout(() => setToast(null), 3000);
    }
  };

  const handleRejectTransaction = async (transactionId: string) => {
    if (!rejectionReason.trim()) {
      setToast({ type: 'error', message: 'Alasan penolakan wajib diisi' });
      return;
    }

    setIsProcessing(true);
    try {
      await apiFetch(`/api/transactions/${transactionId}/reject`, {
        method: 'POST',
        body: JSON.stringify({ rejection_reason: rejectionReason })
      });
      setTransactions(prev => prev.map(t => t.id === transactionId ? { ...t, status: 'rejected', rejectionReason, updatedAt: new Date().toISOString() } : t));
      // Gudang tidak approve -> notifikasi ke mitra & sales
      try {
        const recipients = await apiFetch(`/api/transactions/${transactionId}/recipients`);
        const tx = transactions.find(t => t.id === transactionId);
        if (tx) {
          if (recipients?.mitra) await sendTransactionNotification(recipients.mitra, transactionId, 'rejected', tx.mitraName);
          if (recipients?.sales) await sendTransactionNotification(recipients.sales, transactionId, 'rejected', tx.mitraName);
        }
      } catch (e) {
        console.warn('Gagal kirim WA (gudang reject)', e);
      }
      setToast({ type: 'success', message: 'Transaksi berhasil ditolak!' });
      setIsModalOpen(false);
      setRejectionReason('');
    } catch (error) {
      console.error('Error rejecting transaction:', error);
      setToast({ type: 'error', message: 'Gagal menolak transaksi' });
    } finally {
      setIsProcessing(false);
      if (toast) setTimeout(() => setToast(null), 3000);
    }
  };

  const handleShipTransaction = async (transactionId: string) => {
    if (!shippingDocument) {
      setToast({ type: 'error', message: 'Dokumen pengiriman wajib diupload' });
      return;
    }

    setIsProcessing(true);
    try {
      const formData = new FormData();
      formData.append('shipping_document', shippingDocument);
      const updated = await apiFetch(`/api/transactions/${transactionId}/ship`, {
        method: 'POST',
        body: formData
      });
      setTransactions(prev => prev.map(t => t.id === transactionId ? { ...t, status: 'shipped', shippingDocument: updated.shipping_document || shippingDocument as any, updatedAt: new Date().toISOString() } : t));
      setToast({ type: 'success', message: 'Status berhasil diubah menjadi sedang dikirim!' });
      // Kirim WA ke mitra dan sales
      try {
        const recipients = await apiFetch(`/api/transactions/${transactionId}/recipients`);
        const tx = transactions.find(t => t.id === transactionId);
        if (tx) {
          if (recipients?.mitra) await sendTransactionNotification(recipients.mitra, transactionId, 'shipped', tx.mitraName);
          if (recipients?.sales) await sendTransactionNotification(recipients.sales, transactionId, 'shipped', tx.mitraName);
        }
      } catch (e) {
        console.warn('Gagal kirim WA shipped', e);
      }
      setIsModalOpen(false);
      setShippingDocument('');
    } catch (error) {
      console.error('Error updating shipping status:', error);
      setToast({ type: 'error', message: 'Gagal mengupdate status pengiriman' });
    } finally {
      setIsProcessing(false);
      if (toast) setTimeout(() => setToast(null), 3000);
    }
  };

  const handleDeliverTransaction = async (transactionId: string) => {
    if (!deliveryDocument) {
      setToast({ type: 'error', message: 'Dokumen penerimaan wajib diupload' });
      return;
    }

    setIsProcessing(true);
    try {
      const formData = new FormData();
      formData.append('delivery_document', deliveryDocument);
      const updated = await apiFetch(`/api/transactions/${transactionId}/deliver`, {
        method: 'POST',
        body: formData
      });
      setTransactions(prev => prev.map(t => t.id === transactionId ? { ...t, status: 'delivered', deliveryDocument: updated.delivery_document || deliveryDocument as any, updatedAt: new Date().toISOString() } : t));
      setToast({ type: 'success', message: 'Status berhasil diubah menjadi sudah diterima!' });
      // Kirim WA ke mitra dan sales
      try {
        const recipients = await apiFetch(`/api/transactions/${transactionId}/recipients`);
        const tx = transactions.find(t => t.id === transactionId);
        if (tx) {
          if (recipients?.mitra) await sendTransactionNotification(recipients.mitra, transactionId, 'delivered', tx.mitraName);
          if (recipients?.sales) await sendTransactionNotification(recipients.sales, transactionId, 'delivered', tx.mitraName);
        }
      } catch (e) {
        console.warn('Gagal kirim WA delivered', e);
      }
      setIsModalOpen(false);
      setDeliveryDocument('');
    } catch (error) {
      console.error('Error updating delivery status:', error);
      setToast({ type: 'error', message: 'Gagal mengupdate status penerimaan' });
      if (toast) setTimeout(() => setToast(null), 3000);
    } finally {
      setIsProcessing(false);
    }
  };

  const openModal = (transaction: Transaction) => {
    setSelectedTransaction(transaction);
    setIsModalOpen(true);
    setRejectionReason('');
    setShippingDocument('');
    setDeliveryDocument('');
  };

  const closeModal = () => {
    setIsModalOpen(false);
    setSelectedTransaction(null);
    setRejectionReason('');
    setShippingDocument('');
    setDeliveryDocument('');
  };

  const getTitle = () => {
    if (userRole === 'owner') return 'Approval Diskon & Monitoring';
    if (userRole === 'admin_gudang') return 'Management Transaksi';
    if (userRole === 'sales') return 'Transaksi Saya';
    return 'Semua Transaksi';
  };

  { toast && <Toast type={toast.type} message={toast.message} /> }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900">{getTitle()}</h1>
      </div>

      {/* Transactions Table */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  ID Transaksi
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Mitra
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Total
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Tanggal
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Aksi
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredTransactions.map((transaction) => (
                <tr key={transaction.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">{transaction.id}</div>
                    {transaction.discountType !== 'none' && (
                      <div className="text-xs text-orange-600">
                        Diskon: {
                          (() => {
                            if (transaction.discountType === 'per_item') {
                              const sumItemDisc = (transaction.items || []).reduce((acc, it) => acc + (Number(it.discount) || 0), 0);
                              return formatCurrency(sumItemDisc);
                            }
                            return formatCurrency(transaction.discountValue || 0);
                          })()
                        }
                      </div>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{transaction.mitraName}</div>
                    <div className="text-xs text-gray-500">{transaction.salesName}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">{
                      (() => {
                        const subtotal = transaction.subtotal || 0;
                        const disc = transaction.discountType === 'per_item'
                          ? (transaction.items || []).reduce((acc, it) => acc + (Number(it.discount) || 0), 0)
                          : (transaction.discountValue || 0);
                        return formatCurrency(Math.max(0, subtotal - disc));
                      })()
                    }</div>
                    <div className="text-xs text-gray-500 capitalize">{transaction.paymentMethod}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(transaction.status)}`}>
                      {getStatusLabel(transaction.status)}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {formatDate(transaction.createdAt)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                    <button
                      onClick={() => openModal(transaction)}
                      className="text-blue-600 hover:text-blue-900 inline-flex items-center"
                    >
                      <Eye size={16} className="mr-1" />
                      Detail
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {filteredTransactions.length === 0 && (
          <div className="text-center py-12 text-gray-500">
            Tidak ada transaksi yang ditemukan
          </div>
        )}
      </div>

      {/* Transaction Detail Modal */}
      {isModalOpen && selectedTransaction && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-lg bg-white">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-2xl font-bold text-gray-900">
                Detail Transaksi {selectedTransaction.id}
              </h3>
              <div className="flex items-center space-x-2">
                <button
                  onClick={async () => {
                    try {
                      const res = await apiFetch(`/api/transactions/${selectedTransaction.id}/invoice.pdf`, { responseType: 'blob' });
                      const blob = await (res as Response).blob();
                      const url = URL.createObjectURL(blob);
                      const a = document.createElement('a');
                      a.href = url;
                      a.download = `invoice-TXN-${selectedTransaction.id}.pdf`;
                      document.body.appendChild(a); a.click(); a.remove();
                      URL.revokeObjectURL(url);
                    } catch (e) { console.error(e); }
                  }}
                  className="inline-flex items-center px-3 py-2 rounded-lg text-white bg-indigo-600 hover:bg-indigo-700"
                >
                  <Download size={16} className="mr-1" />
                  Download PDF
                </button>
                <button
                  onClick={closeModal}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <X size={24} />
                </button>
              </div>
            </div>

            <div className="space-y-6">
              {/* Transaction Info */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Mitra</label>
                    <p className="text-sm text-gray-900">{selectedTransaction.mitraName}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Sales</label>
                    <p className="text-sm text-gray-900">{selectedTransaction.salesName}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Metode Pembayaran</label>
                    <p className="text-sm text-gray-900 capitalize">{selectedTransaction.paymentMethod}</p>
                  </div>
                  {selectedTransaction.dueDate && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Jatuh Tempo</label>
                      <p className="text-sm text-gray-900">{formatDate(selectedTransaction.dueDate)}</p>
                    </div>
                  )}
                </div>

                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Status</label>
                    <span className={`inline-flex px-3 py-1 text-sm font-semibold rounded-full ${getStatusColor(selectedTransaction.status)}`}>
                      {getStatusLabel(selectedTransaction.status)}
                    </span>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Total Transaksi</label>
                    <p className="text-lg font-bold text-gray-900">{
                      (() => {
                        const subtotal = selectedTransaction.subtotal || 0;
                        const disc = selectedTransaction.discountType === 'per_item'
                          ? (selectedTransaction.items || []).reduce((acc, it) => acc + (Number(it.discount) || 0), 0)
                          : (selectedTransaction.discountValue || 0);
                        return formatCurrency(Math.max(0, subtotal - disc));
                      })()
                    }</p>
                  </div>
                  {selectedTransaction.discountType !== 'none' && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Diskon</label>
                      <p className="text-sm text-orange-600">
                        {selectedTransaction.discountType === 'total' ? 'Total' : 'Per Item'}: {
                          (() => {
                            if (selectedTransaction.discountType === 'per_item') {
                              const sumItemDisc = (selectedTransaction.items || []).reduce((acc, it) => acc + (Number(it.discount) || 0), 0);
                              return formatCurrency(sumItemDisc);
                            }
                            return formatCurrency(selectedTransaction.discountValue);
                          })()
                        }
                      </p>
                      <p className="text-xs text-gray-500">Alasan: {selectedTransaction.discountReason}</p>
                    </div>
                  )}
                </div>

                {/* Rejection Reason */}
                {selectedTransaction.status === 'rejected' && selectedTransaction.rejectionReason && (
                  <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                    <p className="text-sm text-red-700">
                      Alasan Penolakan Owner: <span className="font-medium">{selectedTransaction.rejectionReason}</span>
                    </p>
                  </div>
                )}

              </div>

              {/* Items */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-3">Items Pesanan</label>
                <div className="border border-gray-200 rounded-lg overflow-hidden">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500">Produk</th>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500">Harga</th>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500">Qty</th>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500">Diskon</th>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500">Total</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200">
                      {selectedTransaction.items.map((item, index) => (
                        <tr key={index}>
                          <td className="px-4 py-2 text-sm text-gray-900">{item.productName}</td>
                          <td className="px-4 py-2 text-sm text-gray-900">{formatCurrency(item.price)}</td>
                          <td className="px-4 py-2 text-sm text-gray-900">{item.quantity}</td>
                          <td className="px-4 py-2 text-sm text-gray-900">{
                            selectedTransaction.discountType === 'per_item'
                              ? formatCurrency(item.discount)
                              : formatCurrency(Math.round(((item.price * item.quantity) / (selectedTransaction.subtotal || 1)) * selectedTransaction.discountValue))
                          }</td>
                          <td className="px-4 py-2 text-sm font-medium text-gray-900">{
                            (() => {
                              const base = item.price * item.quantity;
                              const disc = selectedTransaction.discountType === 'per_item'
                                ? item.discount
                                : Math.round(((item.price * item.quantity) / (selectedTransaction.subtotal || 1)) * selectedTransaction.discountValue);
                              return formatCurrency(base - disc);
                            })()
                          }</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>

              {/* Documents */}
              {(selectedTransaction.shippingDocument || selectedTransaction.deliveryDocument) && (
                <div className="space-y-3">
                  <label className="block text-sm font-medium text-gray-700">Dokumen</label>
                  {selectedTransaction.shippingDocument && (
                    <div className="flex items-center space-x-2">
                      <FileText size={16} className="text-blue-500" />
                      <a
                        href={selectedTransaction.shippingDocument}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-600 hover:text-blue-800 text-sm"
                      >
                        Dokumen Pengiriman
                      </a>
                    </div>
                  )}
                  {selectedTransaction.deliveryDocument && (
                    <div className="flex items-center space-x-2">
                      <FileText size={16} className="text-green-500" />
                      <a
                        href={selectedTransaction.deliveryDocument}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-green-600 hover:text-green-800 text-sm"
                      >
                        Dokumen Penerimaan
                      </a>
                    </div>
                  )}
                </div>
              )}

              {/* Actions based on role and status */}
              <div className="border-t pt-6">
                {/* Owner Actions - Discount Approval */}
                {userRole === 'owner' && selectedTransaction.status === 'pending_owner' && (
                  <div className="space-y-4">
                    <h4 className="text-lg font-medium text-gray-900">Approval Diskon</h4>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Alasan Penolakan (jika ditolak)
                      </label>
                      <textarea
                        value={rejectionReason}
                        onChange={(e) => setRejectionReason(e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500"
                        rows={3}
                        placeholder="Berikan alasan jika diskon ditolak..."
                      />
                    </div>
                    <div className="flex space-x-4">
                      <button
                        onClick={() => handleApproveDiscount(selectedTransaction.id)}
                        disabled={isProcessing}
                        className="flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50"
                      >
                        <Check size={16} className="mr-2" />
                        {isProcessing ? 'Memproses...' : 'Setujui Diskon'}
                      </button>
                      <button
                        onClick={() => handleRejectDiscount(selectedTransaction.id)}
                        disabled={isProcessing}
                        className="flex items-center px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50"
                      >
                        <X size={16} className="mr-2" />
                        {isProcessing ? 'Memproses...' : 'Tolak Diskon'}
                      </button>
                    </div>
                  </div>
                )}

                {/* Admin Gudang Actions */}
                {userRole === 'admin_gudang' && (
                  <>
                    {selectedTransaction.status === 'pending_gudang' && (
                      <div className="space-y-4">
                        <h4 className="text-lg font-medium text-gray-900">Approval Transaksi</h4>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Alasan Penolakan (jika ditolak)
                          </label>
                          <textarea
                            value={rejectionReason}
                            onChange={(e) => setRejectionReason(e.target.value)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500"
                            rows={3}
                            placeholder="Berikan alasan jika transaksi ditolak..."
                          />
                        </div>
                        <div className="flex space-x-4">
                          <button
                            onClick={() => handleApproveTransaction(selectedTransaction.id)}
                            disabled={isProcessing}
                            className="flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50"
                          >
                            <Check size={16} className="mr-2" />
                            {isProcessing ? 'Memproses...' : 'Setujui Transaksi'}
                          </button>
                          <button
                            onClick={() => handleRejectTransaction(selectedTransaction.id)}
                            disabled={isProcessing}
                            className="flex items-center px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50"
                          >
                            <X size={16} className="mr-2" />
                            {isProcessing ? 'Memproses...' : 'Tolak Transaksi'}
                          </button>
                        </div>
                      </div>
                    )}

                    {selectedTransaction.status === 'approved' && (
                      <div className="space-y-4">
                        <h4 className="text-lg font-medium text-gray-900">Update Status - Kirim Barang</h4>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Upload Dokumen Pengiriman
                          </label>
                          <input
                            type="file"
                            onChange={(e) => setShippingDocument(e.target.files && e.target.files[0] ? e.target.files[0] as any : '')}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500"
                            accept="image/*,application/pdf"
                          />
                        </div>
                        <button
                          onClick={() => handleShipTransaction(selectedTransaction.id)}
                          disabled={isProcessing}
                          className="flex items-center px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50"
                        >
                          <Truck size={16} className="mr-2" />
                          {isProcessing ? 'Memproses...' : 'Update ke Sedang Dikirim'}
                        </button>
                      </div>
                    )}

                    {selectedTransaction.status === 'shipped' && (
                      <div className="space-y-4">
                        <h4 className="text-lg font-medium text-gray-900">Update Status - Barang Diterima</h4>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Upload Dokumen Penerimaan
                          </label>
                          <input
                            type="file"
                            onChange={(e) => setDeliveryDocument(e.target.files && e.target.files[0] ? e.target.files[0] as any : '')}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500"
                            accept="image/*,application/pdf"
                          />
                        </div>
                        <button
                          onClick={() => handleDeliverTransaction(selectedTransaction.id)}
                          disabled={isProcessing}
                          className="flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50"
                        >
                          <CheckCircle size={16} className="mr-2" />
                          {isProcessing ? 'Memproses...' : 'Update ke Sudah Diterima'}
                        </button>
                      </div>
                    )}
                  </>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TransactionList;