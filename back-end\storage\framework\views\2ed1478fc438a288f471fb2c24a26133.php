<!doctype html>
<html lang="id">

<head>
    <meta charset="utf-8" />
    <style>
        @page {
            size: A4 landscape;
            margin: 8mm;
        }

        body {
            font-family: Courier, "Courier New", monospace;
            font-size: 10pt;
            color: #000;
        }

        .header-title {
            font-weight: 700;
            font-size: 12pt;
        }

        .muted {
            color: #666;
        }

        .title {
            text-align: center;
            font-weight: 700;
            font-size: 12pt;
            margin: 4mm 0 4mm;
        }

        .row {
            display: table;
            width: 100%;
        }

        .col {
            display: table-cell;
            vertical-align: top;
        }

        .w-60 {
            width: 60%;
        }

        .w-40 {
            width: 40%;
        }

        .mt-4 {
            margin-top: 4mm;
        }

        .mt-6 {
            margin-top: 6mm;
        }

        .mb-2 {
            margin-bottom: 2mm;
        }

        .mb-4 {
            margin-bottom: 4mm;
        }

        .divider {
            border-top: 0.2mm dashed #999;
            margin: 2.5mm 0;
        }

        table {
            width: 33%;
            border-collapse: collapse;
        }

        .header-table {
            width: 100%;
            border-collapse: collapse;
        }

        .header-table td {
            padding: 0;
        }

        .abs-header div {
            white-space: nowrap;
        }

        thead th {
            border-bottom: 0.2mm dashed #999;
            padding: 2.5mm;
            text-align: left;
            font-weight: 700;
        }

        tbody td {
            border-bottom: 0.2mm dashed #bbb;
            padding: 2.5mm;
        }

        .right {
            text-align: right;
        }

        .box {
            border: 0.2mm dashed #999;
            padding: 3mm;
        }

        .small {
            font-size: 9pt;
        }

        .signature {
            margin-top: 14mm;
        }

        .signature .line {
            margin-top: 14mm;
            border-top: 0.2mm dashed #999;
            width: 40mm;
        }

        .col-no {
            width: 10mm;
        }

        .col-kode {
            width: 50mm;
        }

        .col-qty {
            width: 50mm;
        }

        .col-harga {
            width: 50mm;
        }

        .col-total {
            width: 32mm;
        }

        .logo {
            max-width: 60mm;
            max-height: 20mm;
            object-fit: contain;
        }
    </style>
</head>

<body>
    <table>
        <tr>
            <td style="vertical-align:top; width: 40%;">
                <div class="header-title">FAKTUR PENJUALAN</div>
                <div class="header-title"><?php echo e(strtoupper($biz)); ?></div>
                <?php if(!empty($addr)): ?>
                    <div class="small"><?php echo e($addr); ?></div>
                <?php endif; ?>
            </td>
            <td style="vertical-align:top; text-align:center; width: 20%;">
                <?php if(!empty($logo)): ?>
                    <?php
                        // Convert URL to file path for PDF generation
                        $logoSrc = null;
                        if (strpos($logo, '/storage/') !== false) {
                            // Extract relative path from URL
                            $relativePath = substr($logo, strpos($logo, '/storage/') + 9);
                            $logoPath = storage_path('app/public/' . $relativePath);

                            if (file_exists($logoPath)) {
                                $logoData = base64_encode(file_get_contents($logoPath));
                                $logoMime = mime_content_type($logoPath);
                                $logoSrc = "data:$logoMime;base64,$logoData";
                            }
                        }
                    ?>
                    <?php if($logoSrc): ?>
                        <img src="<?php echo e($logoSrc); ?>" alt="Logo" class="logo" />
                    <?php endif; ?>
                <?php endif; ?>
            </td>
            <td style="vertical-align:top; text-align:right; width: 40%;">
                <div>No Transaksi : <?php echo e(sprintf('%06d/JUT/M%02d%02d', $tx->id, now()->month, now()->year % 100)); ?></div>
                <div>Tanggal : <?php echo e(optional($tx->created_at)->format('d/m/Y H.i') ?? now()->format('d/m/Y H.i')); ?></div>
                <div>User : <?php echo e(strtoupper(optional($tx->sales)->name ?? '-')); ?></div>
                <div>Pelanggan : <?php echo e(strtoupper(optional($tx->mitra)->name ?? '-')); ?></div>
            </td>
        </tr>
    </table>

    <br>
    <hr>

    <table>
        <thead>
            <tr>
                <th class="col-no">No</th>
                <th class="col-kode">Kode Item</th>
                <th>Nama Item</th>
                <th class="col-qty right">Jml Satuan</th>
                <th class="col-harga right">Harga</th>
                <th class="col-total right">Total</th>
            </tr>
        </thead>

        <tbody>
            <?php $__currentLoopData = $tx->items; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $i => $it): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <tr>
                    <td><?php echo e($i + 1); ?></td>
                    <td><?php echo e(optional($it->product)->id ? 'OR-JUP' . $it->product_id . 'L' : '-'); ?></td>
                    <td><?php echo e(strtoupper($it->product_name)); ?></td>
                    <td class="right"><?php echo e(number_format((int) $it->quantity, 2, ',', '.')); ?>

                        <?php echo e(strtoupper(optional($it->product)->unit ?? 'BTL')); ?></td>
                    <td class="right"><?php echo e(number_format((int) $it->price, 2, ',', '.')); ?></td>
                    <td class="right"><?php echo e(number_format((int) $it->total, 2, ',', '.')); ?></td>
                </tr>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </tbody>
    </table>

    <table style="margin-top: 1rem;">
        <tr>
            <td style="width: 95mm">Keterangan :</td>
            <td style="width: 95mm">Jml Item : <?php echo e($tx->items->count()); ?></td>
            <td style="width: 95mm">Total Akhir : <?php echo e(number_format((int) $tx->total, 2, ',', '.')); ?></td>
        </tr>

        <tr>
            <td>
                <table>
                    <tr>
                        <td style="width: 45mm">Hormat Kami</td>
                        <td style="width: 45mm">Penerima</td>
                    </tr>
                </table>
            </td>
            <td>Potongan : <?php echo e(number_format((int) ($tx->discount_value ?? 0), 2, ',', '.')); ?> %</td>
            <td>Deposit : <?php echo e(number_format(0, 2, ',', '.')); ?></td>
        </tr>

        <tr>
            <td style="width: 95mm"></td>
            <td style="width: 95mm">Tanggal Jt : <?php echo e(optional($tx->due_date)->format('d/m/Y') ?? '-'); ?></td>
            <td style="width: 95mm">Tunai : <?php echo e(number_format((int) $tx->total, 2, ',', '.')); ?></td>
        </tr>

        <tr>
            <td style="width: 95mm"></td>
            <td style="width: 95mm"></td>
            <td style="width: 95mm">Kredit : <?php echo e(number_format(0, 2, ',', '.')); ?></td>
        </tr>

        <tr>
            <td>
                <table>
                    <tr>
                        <td style="width: 45mm"><br><br>(....................)</td>
                        <td style="width: 45mm"><br><br>(....................)</td>
                    </tr>
                </table>
            </td>
        </tr>

        <tr>
            <td style="width: 95mm"><br>Terbilang : <?php echo e(terbilang($tx->total)); ?></td>
        </tr>

        <tr>
            <td style="width: 95mm">
                <table>
                    <tr>
                        <td style="width: 45mm"></td>
                        <td style="width: 95mm"><br>* Barang yang sudah dibeli tidak dapat dikembalikan lagi</td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>
</body>

</html>
<?php /**PATH D:\Kasir Pertanian\back-end\resources\views/pdf/invoice.blade.php ENDPATH**/ ?>