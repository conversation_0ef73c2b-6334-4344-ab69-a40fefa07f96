<?php

use Illuminate\Support\Facades\Route;

use App\Http\Controllers\AuthController;

Route::get('/health', fn() => response()->json(['ok' => true]));
// CORS preflight handler for all API routes (helps when proxy blocks preflight to PHP)
Route::options('/{any}', function () {
    return response()->noContent(204);
})->where('any', '.*');


// Public WhatsApp proxy (no auth) to avoid CORS
Route::post('/whatsapp/send', [\App\Http\Controllers\WhatsAppProxyController::class, 'send']);

// Auth
Route::post('/auth/login', [AuthController::class, 'login']);
Route::middleware('auth:sanctum')->group(function () {
    Route::post('/auth/logout', [AuthController::class, 'logout']);
    Route::get('/auth/me', [AuthController::class, 'me']);

    // Dashboard
    Route::get('/dashboard/metrics', [\App\Http\Controllers\DashboardController::class, 'metrics']);

    // Analytics
    Route::get('/analytics/products/monthly', [\App\Http\Controllers\AnalyticsController::class, 'productMonthly']);
    Route::get('/analytics/top-products', [\App\Http\Controllers\AnalyticsController::class, 'topProducts']);
    Route::get('/analytics/top-sales', [\App\Http\Controllers\AnalyticsController::class, 'topSales']);
    Route::get('/analytics/top-outlets', [\App\Http\Controllers\AnalyticsController::class, 'topOutlets']);
    Route::get('/analytics/outlet-products', [\App\Http\Controllers\AnalyticsController::class, 'outletProducts']);

    // Categories
    Route::get('/categories', [\App\Http\Controllers\CategoryController::class, 'index']);
    Route::post('/categories', [\App\Http\Controllers\CategoryController::class, 'store']);
    Route::put('/categories/{category}', [\App\Http\Controllers\CategoryController::class, 'update']);
    Route::delete('/categories/{category}', [\App\Http\Controllers\CategoryController::class, 'destroy']);

    // Receivable payments
    Route::get('/receivable-payments', [\App\Http\Controllers\ReceivablePaymentController::class, 'index']);
    Route::post('/receivable-payments', [\App\Http\Controllers\ReceivablePaymentController::class, 'store']);

    // Products
    Route::get('/products', [\App\Http\Controllers\ProductController::class, 'index']);
    Route::post('/products', [\App\Http\Controllers\ProductController::class, 'store']);
    Route::put('/products/{product}', [\App\Http\Controllers\ProductController::class, 'update']);
    Route::delete('/products/{product}', [\App\Http\Controllers\ProductController::class, 'destroy']);
    Route::post('/products/{product}/adjust-stock', [\App\Http\Controllers\ProductController::class, 'adjustStock']);

    // Placeholder secured group
    Route::get('/secure/ping', fn() => response()->json(['pong' => true]));
    // Mitras
    Route::get('/mitras', [\App\Http\Controllers\MitraController::class, 'index']);
    Route::post('/mitras', [\App\Http\Controllers\MitraController::class, 'store']);
    Route::put('/mitras/{mitra}', [\App\Http\Controllers\MitraController::class, 'update']);
    Route::delete('/mitras/{mitra}', [\App\Http\Controllers\MitraController::class, 'destroy']);
    // Stock logs
    Route::get('/stock-logs', [\App\Http\Controllers\StockLogController::class, 'index']);
    // Transactions
    Route::post('/transactions', [\App\Http\Controllers\TransactionCreateController::class, 'store']);

    Route::get('/transactions', [\App\Http\Controllers\TransactionController::class, 'index']);
    Route::post('/transactions/{transaction}/approve-owner', [\App\Http\Controllers\TransactionController::class, 'approveOwner']);
    Route::post('/transactions/{transaction}/reject', [\App\Http\Controllers\TransactionController::class, 'reject']);
    Route::post('/transactions/{transaction}/approve-gudang', [\App\Http\Controllers\TransactionController::class, 'approveGudang']);
    Route::post('/transactions/{transaction}/ship', [\App\Http\Controllers\TransactionController::class, 'ship']);
    Route::post('/transactions/{transaction}/deliver', [\App\Http\Controllers\TransactionController::class, 'deliver']);
    Route::get('/transactions/{transaction}/recipients', [\App\Http\Controllers\TransactionController::class, 'recipients']);



    // Owner settings
    Route::get('/settings', [\App\Http\Controllers\SettingController::class, 'show']);
    Route::put('/settings', [\App\Http\Controllers\SettingController::class, 'update']);
    Route::post('/settings/logo', [\App\Http\Controllers\SettingController::class, 'uploadLogo']);

    // Users (superadmin scope ideally; for now simple)
    Route::get('/users', [\App\Http\Controllers\UserController::class, 'index']);
    Route::post('/users', [\App\Http\Controllers\UserController::class, 'store']);
    Route::put('/users/{user}', [\App\Http\Controllers\UserController::class, 'update']);
    Route::delete('/users/{user}', [\App\Http\Controllers\UserController::class, 'destroy']);

    // Invoice PDF
    Route::get('/transactions/{transaction}/invoice.pdf', [\App\Http\Controllers\PdfInvoiceController::class, 'show']);
});
